package hackerrank.real_interview.split_the_array.test

import hackerrank.real_interview.split_the_array.findMinimumNumberOfSplitSubarrayProblem
import kotlin.test.assertEquals

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_0() {
    verify(
        inputs = intArrayOf(2),
        expectedOutput = 1
    )
}

private fun sample_1() {
    verify(
        inputs = intArrayOf(2, 3),
        expectedOutput = 2
    )
}

// entire array
private fun sample_2() {
    verify(
        inputs = intArrayOf(2, 2),
        expectedOutput = 1
    )
}

// [2,3,2] n [3,3] or [2] n [3,2,3,3]
private fun sample_3() {
    verify(
        inputs = intArrayOf(2, 3, 2, 3, 3),
        expectedOutput = 2
    )
}

// single item in an array
private fun sample_4() {
    verify(
        inputs = intArrayOf(3, 5, 7, 11, 2),
        expectedOutput = 5
    )
}

// [3], [2, 7, 2], [3]
private fun sample_5() {
    verify(
        inputs = intArrayOf(3, 2, 7, 2, 7),
        expectedOutput = 3
    )
}

private fun verify(
    inputs: IntArray,
    expectedOutput: Int
) {
    val actual = findMinimumNumberOfSplitSubarrayProblem(inputs)
    assertEquals(expectedOutput, actual)
}
