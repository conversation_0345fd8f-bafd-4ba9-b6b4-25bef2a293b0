package hackerrank.real_interview.split_the_array

import java.lang.Integer.min

/**
 * Find the min. number of split subarray which is a good array
 * where good array means the gcd(left most value, right most value) > 1
 *
 * n = size of the array
 * subarray cal[i to j] where 0 <= i <= j < n
 * gcd(val[i], val[j]) > 1 means good
 *
 * 1 <= n <= 10^5
 * 2 <= val[i] <= 10^6
 */
fun findMinimumNumberOfSplitSubarrayProblem(arr: IntArray): Int {
    val gcd = buildGcd(arr)
//    return findResult(gcd, arr, 0, arr.lastIndex)

     val result = Array(arr.size) { IntArray(arr.size) { Int.MAX_VALUE } }
     return findResult(gcd, arr, 0, arr.lastIndex, result)
}

private fun buildGcd(arr: IntArray): Array<IntArray> {
    val max = arr.maxOrNull()!!
    val gcd = Array(max + 1) { IntArray(max + 1) { 0 } }
    for (a in 0..max) {
        for (b in 0..max) {
            gcd[a][b] = when {
                a == 0 -> b
                b == 0 -> a
                a > b -> gcd[a - b][b]
                a == b -> a
                else -> gcd[a][b - a]
            }
        }
    }
    return gcd
}

private fun findResult(gcd: Array<IntArray>, arr: IntArray, startIndex: Int, endIndex: Int): Int {
    if (startIndex == endIndex) {
        return 1
    }
    val headValue = arr[startIndex]
    val tailValue = arr[endIndex]
    if (gcd[headValue][tailValue] > 1) {
        return 1
    }
    val left = findResult(gcd, arr, startIndex, endIndex - 1)
    val right = findResult(gcd, arr, startIndex + 1, endIndex)
    val min = min(left, right)
    return 1 + min
}

private fun findResult(gcd: Array<IntArray>, arr: IntArray, startIndex: Int, endIndex: Int, result: Array<IntArray>): Int {
    if (startIndex == endIndex) {
        result[startIndex][endIndex] = 1
        return 1
    }
    val headValue = arr[startIndex]
    val tailValue = arr[endIndex]
    if (gcd[headValue][tailValue] > 1) {
        result[startIndex][endIndex] = 1
        return 1
    }
    val left = if (result[startIndex][endIndex - 1] == Int.MAX_VALUE) {
        val leftResult = findResult(gcd, arr, startIndex, endIndex - 1, result)
        result[startIndex][endIndex - 1] = leftResult
        leftResult
    } else {
        result[startIndex][endIndex - 1]
    }
    val right = if (result[startIndex + 1][endIndex] == Int.MAX_VALUE) {
        val rightResult = findResult(gcd, arr, startIndex + 1, endIndex, result)
        result[startIndex + 1][endIndex] = rightResult
        rightResult
    } else {
        result[startIndex + 1][endIndex]
    }
    val min = min(left, right)
    return 1 + min
}
