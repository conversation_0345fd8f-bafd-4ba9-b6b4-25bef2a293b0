package hackerrank.practice.interview_preparation_kit.greedy_algorithms.reverse_shuffle_merge

import java.util.*
import kotlin.collections.*

/**
 * https://www.hackerrank.com/challenges/reverse-shuffle-merge/problem
 *
 * Solution: https://www.youtube.com/watch?v=_QQe2TQ2o_4
 */

// Complete the reverseShuffleMerge function below.
fun reverseShuffleMerge(s: String): String {
    val used = mutableMapOf<Char, Int>()
    val notProcessed = mutableMapOf<Char, Int>()
    val required = mutableMapOf<Char, Int>()
    for (letter in s) {
        if (notProcessed.containsKey(letter)) {
            notProcessed[letter] = notProcessed[letter]!! + 1
        } else {
            notProcessed[letter] = 1
        }
    }

    for (letter in notProcessed.keys) {
        used[letter] = 0
        required[letter] = notProcessed[letter]!! / 2
    }

    val result = mutableListOf<Char>()
    for (index in s.lastIndex downTo 0) {
        val letter = s[index]
        notProcessed[letter] = notProcessed[letter]!! - 1

        val numberOfLetterWeNeed = required[letter]!!
        val numberOfLetterWeUsed = used[letter]!!
        val needMoreLetter = numberOfLetterWeNeed > numberOfLetterWeUsed
        if (needMoreLetter) {
            used[letter] = numberOfLetterWeUsed + 1
            result.add(letter)

            while (result.size > 1 && result.size - 2 >= 0) {
                val previousLetterIndex = result.size - 2
                val previousLetter = result[previousLetterIndex]
                // used[previousLetter]!! - 1 means if we put back the previous letter
                val canAddBackPreviousLetterLater = used[previousLetter]!! - 1 + notProcessed[previousLetter]!! >= required[previousLetter]!!
                if (previousLetter > letter && canAddBackPreviousLetterLater) {
                    result.removeAt(previousLetterIndex)
                    used[previousLetter] = used[previousLetter]!! - 1
                } else {
                    break
                }
            }
        }
    }

    return result.joinToString(separator = "")
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val s = scan.nextLine()

    val result = reverseShuffleMerge(s)

    println(result)
}
