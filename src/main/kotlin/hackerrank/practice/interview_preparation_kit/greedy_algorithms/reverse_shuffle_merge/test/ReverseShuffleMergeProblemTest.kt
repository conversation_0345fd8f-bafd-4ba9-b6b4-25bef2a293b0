package hackerrank.practice.interview_preparation_kit.greedy_algorithms.reverse_shuffle_merge.test

import hackerrank.practice.util.sampleTemplate
import hackerrank.practice.interview_preparation_kit.greedy_algorithms.reverse_shuffle_merge.main as mainFunction

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "eggegg",
        expectedOutput = "egg"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "abcdefgabcdefg",
        expectedOutput = "agfedcb"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "aeiouuoiea",
        expectedOutput = "aeiou"
    )
}

private fun sample_3() {
    sample(
        sampleNumber = 3,
        inputs = "aabb",
        expectedOutput = "ba"
    )
}

private fun sample_4() {
    sample(
        sampleNumber = 4,
        inputs = "bdabaceadaedaaaeaecdeadababdbeaeeacacaba",
        expectedOutput = "aaaaaabaaceededecbdb"
    )
}

private fun sample_5() {
    sample(
        sampleNumber = 5,
        inputs = "djjcddjggbiigjhfghehhbgdigjicafgjcehhfgifadihiajgciagicdahcbajjbhifjiaajigdgdfhdiijjgaiejgegbbiigida",
        expectedOutput = "aaaaabccigicgjihidfiejfijgidgbhhehgfhjgiibggjddjjd"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}