package hackerrank.practice.interview_preparation_kit.greedy_algorithms.luck_balance

/**
 * https://www.hackerrank.com/challenges/luck-balance/problem
 */

fun main() {
    val k = 3
    val contests = arrayOf(
        arrayOf(5, 1),
        arrayOf(2, 1),
        arrayOf(1, 1),
        arrayOf(8, 1),
        arrayOf(10, 0),
        arrayOf(5, 0)
    )
    println(luckBalance(k, contests))
}

fun luckBalance(k: Int, contests: Array<Array<Int>>): Int {
    val comparator = Comparator { a1: Array<Int>, a2: Array<Int> ->
        val c1 = a2[0].compareTo(a1[0])
        if (c1 == 0) {
            return@Comparator a1[1].compareTo(a2[1])
        } else {
            return@Comparator c1
        }
    }
    val sortedContests = contests.sortedWith(comparator)
    var failInImportantContextCount = 0
    var lucks = 0
    sortedContests.forEach {
        val luck = it[0]
        val isImportant = it[1] == 1

        if (isImportant) {
            if (failInImportantContextCount < k) {
                lucks += luck
                failInImportantContextCount++
            } else {
                lucks -= luck
            }
        } else {
            lucks += luck
        }
    }

    return lucks
}
