package hackerrank.practice.interview_preparation_kit.dynamic_programming.abbreviation

import java.util.*

// https://www.hackerrank.com/challenges/abbr/problem?h_l=interview&playlist_slugs%5B%5D=interview-preparation-kit&playlist_slugs%5B%5D=dynamic-programming
// example: a=AbcDE, b=ABDE
val dp = mutableMapOf<String, String>()
fun abbreviation(a: String, b: String): String {
    dp.clear()
    return abbreviation(a, b, a.lastIndex, b.lastIndex)
}

fun abbreviation(aString: String, bString: String, aEndIndex: Int, bEndIndex: Int): String {
    val key = "$aEndIndex-$bEndIndex"
    if (dp.containsKey(key)) {
        return dp[key]!!
    }

    val aIsEmpty = aEndIndex == -1
    val bIsEmpty = bEndIndex == -1
    val result = if (aIsEmpty && bIsEmpty) {
        "YES"
    } else if (aIsEmpty && !bIsEmpty) {
        "NO"
    } else if (!aIsEmpty && bIsEmpty) {
        val a = aString.substring(0, aEndIndex + 1)
        if (a.isAllLowerCase()) {
            "YES"
        } else {
            "NO"
        }
    } else {
        val lastCharOfA = aString[aEndIndex]
        val lastCharOfB = bString[bEndIndex]
        if (lastCharOfA == lastCharOfB) {
            // same char
            abbreviation(aString, bString, aEndIndex - 1, bEndIndex - 1)
        } else if (lastCharOfA.isLowerCase() && lastCharOfA.uppercaseChar() != lastCharOfB) {
            // can remove the char
            abbreviation(aString, bString, aEndIndex - 1, bEndIndex)
        } else if (lastCharOfA.isLowerCase() && lastCharOfA.uppercaseChar() == lastCharOfB) {
            // can remove the char OR make the char to be upper case
            val resultOfRemove = abbreviation(aString, bString, aEndIndex - 1, bEndIndex)
            val resultOfUpper = abbreviation(aString, bString, aEndIndex - 1, bEndIndex - 1)
            if (resultOfRemove == "YES" || resultOfUpper == "YES") {
                "YES"
            } else {
                "NO"
            }
        } else {
            "NO"
        }
    }

    dp[key] = result
    return result
}

private fun String.isAllLowerCase(): Boolean {
    for (c in this) {
        if (c.isUpperCase()) return false
    }
    return true
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val q = scan.nextLine().trim().toInt()

    for (qItr in 1..q) {
        val a = scan.nextLine()

        val b = scan.nextLine()

        val result = abbreviation(a, b)

        println(result)
    }
}
