package hackerrank.practice.interview_preparation_kit.sorting.mark_and_toys

import java.util.*

// Complete the maximumToys function below.
fun maximumToys(prices: IntArray, amountToSpend: Int): Int {
    val sortedPrices = prices.sorted()
    val firstIndexOfPriceMoreThanAmountToSpend = sortedPrices.indexOfFirst { it > amountToSpend }
    val sortedAndFilteredPrices = if (firstIndexOfPriceMoreThanAmountToSpend > -1) {
        sortedPrices.slice(0 until firstIndexOfPriceMoreThanAmountToSpend)
    } else {
        sortedPrices
    }

    var result = 0
    for (pricesSizeForCalculation in sortedAndFilteredPrices.size downTo 0) {
        var subtractedResult = amountToSpend
        for (index in pricesSizeForCalculation - 1 downTo 0) {
            subtractedResult -= sortedAndFilteredPrices[index]
            if (subtractedResult < 0) {
                break
            }
        }

        if (subtractedResult >= 0) {
            result = pricesSizeForCalculation
            break
        }
    }

    return result
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val nk = scan.nextLine().split(" ")

    val n = nk[0].trim().toInt()

    val k = nk[1].trim().toInt()

    val prices = scan.nextLine().split(" ").map { it.trim().toInt() }.toIntArray()

    val result =
        maximumToys(prices, k)

    println(result)
}
