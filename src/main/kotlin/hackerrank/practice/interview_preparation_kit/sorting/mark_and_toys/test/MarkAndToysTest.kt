package hackerrank.practice.interview_preparation_kit.sorting.mark_and_toys.test

import hackerrank.practice.util.readAsText
import hackerrank.practice.util.testTemplate
import hackerrank.practice.interview_preparation_kit.sorting.mark_and_toys.main as mainFunction

fun main() {
    sample_1()
    sample_2()
    sample_3()
    case_15()
}

fun sample_1() {
    val inputs = "4 7\n" +
            "1 2 3 4"
    val expectedOutput = "3"
    testTemplate("sample_1", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_2() {
    val inputs = "7 50\n" +
            "1 12 5 111 200 1000 10"
    val expectedOutput = "4"
    testTemplate("sample_2", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_3() {
    val inputs = "5 15\n" +
            "3 7 2 9 4"
    val expectedOutput = "3"
    testTemplate("sample_3", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun case_15() {
    val inputs = "/hackerrank/proactice/interview_preparation_kit/sorting/mark_and_toys/test/case_15/case_15_input.txt".readAsText()
    val expectedOutput = "1293"
    testTemplate("sample_3", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

