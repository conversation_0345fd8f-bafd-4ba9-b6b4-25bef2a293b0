package hackerrank.practice.interview_preparation_kit.sorting.merge_sort_counting_Inversions

import java.util.*


// reference: https://www.geeksforgeeks.org/counting-inversions/

// Complete the countInversions function below.
fun countInversions(arr: Array<Int>): Long {
    return mergeSortAndCount(arr, startIndex = 0, endIndex = arr.lastIndex)
}

// Merge sort function
private fun mergeSortAndCount(arr: Array<Int>, startIndex: Int, endIndex: Int): Long {
    // Keeps track of the inversion count at a particular node of the recursion tree
    var count = 0L
    if (startIndex < endIndex) {
        val middleIndex = (startIndex + endIndex) / 2

        // Total inversion count = left subarray count + right subarray count + merge count

        // Left subarray count
        count += mergeSortAndCount(arr, startIndex, middleIndex)

        // Right subarray count
        count += mergeSortAndCount(arr, middleIndex + 1, endIndex)

        // Merge count
        count += mergeAndCount(arr, startIndex, middleIndex, endIndex)
    }
    return count
}

// Function to count the number of inversions during the merge process
private fun mergeAndCount(arr: Array<Int>, startIndex: Int, middleIndex: Int, endIndex: Int): Long {
    // Left subarray
    val left = arr.copyOfRange(startIndex, middleIndex + 1)

    // Right subarray
    val right = arr.copyOfRange(middleIndex + 1, endIndex + 1)
    var leftArrayIndex = 0
    var rightArrayIndex = 0
    var index = startIndex
    var swaps = 0L
    while (leftArrayIndex <= left.lastIndex && rightArrayIndex <= right.lastIndex) {
        if (left[leftArrayIndex] <= right[rightArrayIndex]) {
            arr[index++] = left[leftArrayIndex++]
        } else {
            arr[index++] = right[rightArrayIndex++]
            /*
             * At any step in merge(), if a[i] is greater than a[j], then there are (mid – i) inversions.
             * because left and right subarrays are sorted, so all the remaining elements in left-subarray (a[i+1], a[i+2] … a[mid])
             * will be greater than a[j]
             */
            swaps += (middleIndex + 1) - (startIndex + leftArrayIndex)
        }
    }

    // Fill from the rest of the left subarray
    while (leftArrayIndex < left.size) {
        arr[index++] = left[leftArrayIndex++]
    }

    // Fill from the rest of the right subarray
    while (rightArrayIndex < right.size) {
        arr[index++] = right[rightArrayIndex++]
    }
    return swaps
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val t = scan.nextLine().trim().toInt()

    for (tItr in 1..t) {
        val n = scan.nextLine().trim().toInt()

        val arr = scan.nextLine().split(" ").map { it.trim().toInt() }.toTypedArray()

        val result = countInversions(arr)

        println(result)
    }
}
