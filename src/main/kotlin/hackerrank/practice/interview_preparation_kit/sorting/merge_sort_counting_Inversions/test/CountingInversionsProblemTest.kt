package hackerrank.practice.interview_preparation_kit.sorting.merge_sort_counting_Inversions.test

import hackerrank.practice.util.sampleTemplate
import hackerrank.practice.interview_preparation_kit.sorting.merge_sort_counting_Inversions.main as mainFunction

fun main() {
    sample_0()
    sample_1()
    sample_2()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "2\n" +
                "5\n" +
                "1 1 1 2 2\n" +
                "5\n" +
                "2 1 3 1 2",
        expectedOutput = "0\n" +
                "4"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "1\n" +
                "4\n" +
                "8 4 2 1",
        expectedOutput = "6"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 1,
        inputs = "1\n" +
                "3\n" +
                "3 1 2",
        expectedOutput = "2"
    )
}


private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}
