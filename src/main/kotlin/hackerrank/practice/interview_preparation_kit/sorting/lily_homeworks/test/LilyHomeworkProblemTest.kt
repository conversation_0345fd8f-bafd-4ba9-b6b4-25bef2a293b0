package hackerrank.practice.interview_preparation_kit.sorting.lily_homeworks.test

import hackerrank.practice.util.testTemplate

fun main() {
    sampke_0()
    sampke_1()
}

private fun sampke_0() {
    val inputs = "4\n" +
            "2 5 3 1"
    val expectedOutput = "2"
    testTemplate(
        "normal ascending sort case",
        inputs,
        expectedOutput
    ) { args ->
        hackerrank.practice.interview_preparation_kit.sorting.lily_homeworks.main(args)
    }
}

private fun sampke_1() {
    val inputs = "5\n" +
            "3 4 2 5 1"
    val expectedOutput = "2"
    testTemplate("test reserve sort case", inputs, expectedOutput) { args ->
        hackerrank.practice.interview_preparation_kit.sorting.lily_homeworks.main(args)
    }
}