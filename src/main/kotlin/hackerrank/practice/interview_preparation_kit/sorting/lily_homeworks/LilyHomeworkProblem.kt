package hackerrank.practice.interview_preparation_kit.sorting.lily_homeworks

import java.util.*
import kotlin.collections.*
import kotlin.io.*
import kotlin.text.*

// https://www.hackerrank.com/challenges/lilys-homework/problem?h_r=next-challenge&h_v=zen

/**
 * the number in a array is distinct
 * 1 <= n <= 32
 * 1 <= max number <= 512
 * SUM of the difference between consecutive numbers (in absolute value) is minimal
 */
fun lilysHomework(arr: Array<Int>): Int {
    val sortedArray = Array(arr.size) { 0 }
    val sortedArrayInDescending = Array(arr.size) { 0 }
    val copyArr1 = Array(arr.size) { 0 }
    val copyArr2 = Array(arr.size) { 0 }
    // the key of the performance is using a map for look up the index instead of using Array.indexOf
    val valueToIndexMap1 = mutableMapOf<Int, Int>()
    val valueToIndexMap2 = mutableMapOf<Int, Int>()
    arr.forEachIndexed { index, value ->
        sortedArray[index] = value
        sortedArrayInDescending[index] = value
        copyArr1[index] = value
        copyArr2[index] = value
        valueToIndexMap1[value] = index
        valueToIndexMap2[value] = index
    }
    sortedArray.sort()
    sortedArrayInDescending.sortDescending()

    val countForAscendingArray =
        count(
            sortedArray,
            copyArr1,
            valueToIndexMap1
        )
    val countForDescendingArray =
        count(
            sortedArrayInDescending,
            copyArr2,
            valueToIndexMap2
        )

    return countForAscendingArray.coerceAtMost(countForDescendingArray)
}

private fun count(sortedArray: Array<Int>, originalArray: Array<Int>, valueToIndexMap: MutableMap<Int, Int>): Int {
    var count = 0
    sortedArray.forEachIndexed { expectedIndex, value ->
        val actualIndexOfTheValue = valueToIndexMap.getValue(value)
        if (actualIndexOfTheValue != expectedIndex) {
            val valueInExpectedIndex = originalArray[expectedIndex]
            // swap the value
            originalArray[expectedIndex] = value
            valueToIndexMap[value] = expectedIndex
            originalArray[actualIndexOfTheValue] = valueInExpectedIndex
            valueToIndexMap[valueInExpectedIndex] = actualIndexOfTheValue
            count++
        }
    }
    return count
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val n = scan.nextLine().trim().toInt()

    val arr = scan.nextLine().split(" ").map{ it.trim().toInt() }.toTypedArray()

    val result =
        lilysHomework(arr)

    println(result)
}
