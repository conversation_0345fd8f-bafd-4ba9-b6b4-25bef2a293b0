package hackerrank.practice.interview_preparation_kit.sorting.comparator.test

import hackerrank.practice.util.testTemplate

import hackerrank.practice.interview_preparation_kit.sorting.comparator.Solution.main as mainFunction

fun main() {
    sample0()
}

fun sample0() {
    val inputs = "5\n" +
            "amy 100\n" +
            "david 100\n" +
            "heraldo 50\n" +
            "aakansha 75\n" +
            "aleksa 150"
    val expectedOutput = "aleksa 150\n" +
            "amy 100\n" +
            "david 100\n" +
            "aakansha 75\n" +
            "heraldo 50"
    testTemplate("sample_0", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}
