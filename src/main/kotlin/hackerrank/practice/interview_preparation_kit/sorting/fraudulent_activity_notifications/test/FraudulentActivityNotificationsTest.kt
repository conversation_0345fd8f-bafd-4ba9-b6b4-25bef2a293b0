package hackerrank.practice.interview_preparation_kit.sorting.fraudulent_activity_notifications.test

import hackerrank.practice.util.readAsText
import hackerrank.practice.util.testTemplate
import hackerrank.practice.interview_preparation_kit.sorting.fraudulent_activity_notifications.main as mainFunction

fun main() {
    sample_0()
    sample_1()
    sample_2()
    case_5()
}

fun sample_0() {
    val inputs = "9 5\n" +
            "2 3 4 2 3 6 8 4 5"
    val expectedOutput = "2"
    testTemplate("sample_0", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_1() {
    val inputs = "5 4\n" +
            "1 2 3 4 4"
    val expectedOutput = "0"
    testTemplate("sample_1", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_2() {
    val inputs = "5 3\n" +
            "10 20 30 40 50"
    val expectedOutput = "1"
    testTemplate("sample_2", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun case_5() {
    val inputs = "/hackerrank/proactice/interview_preparation_kit/sorting/fraudulent_activity_notifications/test/case_5/case_5_input.txt".readAsText()
    val expectedOutput = "926"
    testTemplate("case_5", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}
