package hackerrank.practice.interview_preparation_kit.sorting.fraudulent_activity_notifications

import java.util.*

// Complete the activityNotifications function below.
fun activityNotifications(expenditures: Array<Int>, lookbackDays: Int): Int {
    var notificationCount = 0

    val maxValue = 200
    val sortingCounter = IntArray(maxValue + 1)
    for (index in 0 until lookbackDays) {
        val expenditure = expenditures[index]
        sortingCounter[expenditure]++
    }

    for (expenditureIndex in lookbackDays until expenditures.size) {
        val expenditure = expenditures[expenditureIndex]
        val threshold =
            findThreshold(
                lookbackDays,
                sortingCounter
            )
        if (expenditure >= threshold) {
            notificationCount++
        }

        sortingCounter[expenditure]++

        val firstExpenditure = expenditures[expenditureIndex - lookbackDays]
        sortingCounter[firstExpenditure]--
    }

    return notificationCount
}

private fun findThreshold(lookbackDays: Int, sortingCounter: IntArray): Int {
    var threshold = 0
    val hasEvenItems = lookbackDays % 2 == 0
    val numberOfExpendituresIsMedian = lookbackDays / 2
    if (hasEvenItems) {
        var firstMedianValue: Int? = null
        var secondMedianValue: Int? = null

        var totalNumberOfExpendituresProcessed = 0
        for (expenditure in sortingCounter.indices) {
            val numberOfExpenditures = sortingCounter[expenditure]
            totalNumberOfExpendituresProcessed += numberOfExpenditures
            if (firstMedianValue == null && totalNumberOfExpendituresProcessed >= numberOfExpendituresIsMedian) {
                firstMedianValue = expenditure
            }
            if (secondMedianValue == null && totalNumberOfExpendituresProcessed >= numberOfExpendituresIsMedian + 1) {
                secondMedianValue = expenditure
                break
            }
        }
        threshold = firstMedianValue!! + secondMedianValue!!
    } else {
        var totalNumberOfExpendituresProcessed = 0
        for (expenditure in sortingCounter.indices) {
            totalNumberOfExpendituresProcessed += sortingCounter[expenditure]
            if (totalNumberOfExpendituresProcessed > numberOfExpendituresIsMedian) {
                threshold = 2 * expenditure
                break
            }
        }
    }
    return threshold
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val nd = scan.nextLine().split(" ")

    val n = nd[0].trim().toInt()

    val d = nd[1].trim().toInt()

    val expenditure = scan.nextLine().split(" ").map { it.trim().toInt() }.toTypedArray()

    val result =
        activityNotifications(
            expenditure,
            d
        )

    println(result)
}
