package hackerrank.practice.interview_preparation_kit.dictionaries_and_hashmaps.frequency_queries.test

import hackerrank.practice.util.readAsText
import hackerrank.practice.util.testTemplate
import hackerrank.practice.interview_preparation_kit.dictionaries_and_hashmaps.frequency_queries.main as mainFunction

fun main() {
    sample_0()
    sample_1()
    sample_2()

    case_13()
}

private fun sample_0() {
    val inputs = "8\n" +
            "1 5\n" +
            "1 6\n" +
            "3 2\n" +
            "1 10\n" +
            "1 10\n" +
            "1 6\n" +
            "2 5\n" +
            "3 2"
    val expectedOutput = "0\n" +
            "1"
    testTemplate("sample_0", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

private fun sample_1() {
    val inputs = "4\n" +
            "3 4\n" +
            "2 1003\n" +
            "1 16\n" +
            "3 1"
    val expectedOutput = "0\n" +
            "1"
    testTemplate("sample_1", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

private fun sample_2() {
    val inputs = "10\n" +
            "1 3\n" +
            "2 3\n" +
            "3 2\n" +
            "1 4\n" +
            "1 5\n" +
            "1 5\n" +
            "1 4\n" +
            "3 2\n" +
            "2 4\n" +
            "3 2"
    val expectedOutput = "0\n" +
            "1\n" +
            "1"
    testTemplate("sample_2", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun case_13() {
    val resourcePath = "/hackerrank/proactice/interview_preparation_kit/dictionaries_and_hashmaps/frequency_queries/test/case_13"
    val inputs = "$resourcePath/case_13_input.txt".readAsText()
    val expectedOutput = "$resourcePath/case_13_output.txt".readAsText()

    testTemplate("case_13", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}
