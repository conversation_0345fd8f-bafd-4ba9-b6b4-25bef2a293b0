package hackerrank.practice.interview_preparation_kit.dictionaries_and_hashmaps.frequency_queries

import java.util.*

// Complete the freqQuery function below.
fun freqQuery(queries: Array<IntArray>): Array<Int> {
    val valueToCountMapping = mutableMapOf<Int, Int>()
    val countToValuesMapping = mutableMapOf<Int, MutableList<Int>>()
    val answers = mutableListOf<Int>()

    val remove: (Int, Int) -> Unit = { oldCount, value ->
        if (countToValuesMapping.containsKey(oldCount)) {
            val values = countToValuesMapping[oldCount]!!

            values.remove(value)
            if (values.isEmpty()) {
                countToValuesMapping.remove(oldCount)
            }
        }
    }

    val add: (Int, Int) -> Unit = { newCount, value ->
        if (newCount > 0) {
            countToValuesMapping.compute(newCount) { _, values ->
                val newValues = values?.let {
                    values.add(value)
                    values
                } ?: run {
                    mutableListOf(value)
                }
                newValues
            }
        }
    }

    queries.forEach { query ->
        val command = query[0]
        val value = query[1]

        when (command) {
            1 -> {
                valueToCountMapping.compute(value) { _, oldCount ->
                    val newCount = oldCount?.let {
                        // remove old count
                        remove(oldCount, value)

                        oldCount + 1
                    } ?: 1

                    // add new count
                    add(newCount, value)
                    newCount
                }
            }
            2 -> {
                valueToCountMapping.computeIfPresent(value) { _, oldCount ->
                    // remove old count
                    remove(oldCount, value)

                    val newCount = if (oldCount > 0) oldCount - 1 else 0

                    // add new count
                    add(newCount, value)

                    newCount
                }
            }
            3 -> {
                val frequencyMatch = countToValuesMapping.containsKey(value)
                val answer = if (frequencyMatch) 1 else 0
                answers.add(answer)
            }
            else -> throw IllegalArgumentException("invalid command[$command] is detected")
        }
    }
    return answers.toTypedArray()
}

fun main(args: Array<String>) {
    val q = readLine()!!.trim().toInt()

    val queries = Array(q) { IntArray(2) }

    for (i in 0 until q) {
        val tokenizer = StringTokenizer(readLine()!!, " ")
        queries[i] = intArrayOf(
            tokenizer.nextToken().toInt(),
            tokenizer.nextToken().toInt()
        )
    }

    val ans =
        freqQuery(
            queries
        )

    println(ans.joinToString("\n"))
}
