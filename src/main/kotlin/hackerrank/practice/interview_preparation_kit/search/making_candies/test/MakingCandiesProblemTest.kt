package hackerrank.practice.interview_preparation_kit.search.making_candies.test

import hackerrank.practice.interview_preparation_kit.search.making_candies.main as mainFunction
import hackerrank.practice.util.sampleTemplate

fun main() {
    sample_n1()
    sample_0()
    sample_1()
    sample_2()
    testcase_4()
    testcase_7()
}

private fun sample_n1() {
    sample(
        sampleNumber = -1,
        inputs = "1 2 1 60",
        expectedOutput = "4"
    )
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "3 1 2 12",
        expectedOutput = "3"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "1 1 6 45",
        expectedOutput = "16"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "5184889632 5184889632 20 10000",
        expectedOutput = "1"
    )
}

private fun testcase_4() {
    sample(
        sampleNumber = 4,
        inputs = "1 1 1000000000000 1000000000000",
        expectedOutput = "1000000000000"
    )
}

private fun testcase_7() {
    sample(
        sampleNumber = 7,
        inputs = "1 100 10000000000 1000000000000",
        expectedOutput = "617737754"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}
