package hackerrank.practice.interview_preparation_kit.search.making_candies

import java.util.*
import kotlin.math.pow

// https://www.hackerrank.com/challenges/making-candies/problem
// solution: https://allhackerranksolutions.blogspot.com/2019/02/making-candies-hacker-rank-solution.html

// Complete the minimumPasses function below.
fun minimumPasses(m: Long, w: Long, p: Long, n: Long): Long {
    var lessRounds = 1L
    var moreRounds = 10.0.pow(12.0).toLong()
    while (lessRounds < moreRounds) {
        val mid = (lessRounds + moreRounds) / 2L
        if (canProduceEnoughCandies(m, w, p, n, mid)) {
            moreRounds = mid
        } else {
            lessRounds = mid + 1L
        }
    }
    return lessRounds
}

fun canProduceEnoughCandies(m: Long, w: Long, price: Long, target: Long, mid: Long): Boolean {
    var machines = m
    var workers = w
    var rounds = mid

    if (machines >= (target + workers - 1) / workers) return true
    var cur = machines * workers
    rounds--
    if (rounds == 0L) {
        return false
    }
    while (true) {
        var rem = target - cur
        var rnds = (rem + machines * workers - 1) / (machines * workers)
        if (rnds <= rounds) {
            return true
        }
        if (cur < price) {
            rem = price - cur
            rnds = (rem + machines * workers - 1) / (machines * workers)
            rounds -= rnds
            if (rounds < 1) {
                return false
            }
            cur += rnds * machines * workers
        }
        cur -= price
        if (machines > workers) {
            workers++
        } else {
            machines++
        }
    }
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val mwpn = scan.nextLine().split(" ")

    val m = mwpn[0].trim().toLong()

    val w = mwpn[1].trim().toLong()

    val p = mwpn[2].trim().toLong()

    val n = mwpn[3].trim().toLong()

    val result = minimumPasses(m, w, p, n)

    println(result)
}
