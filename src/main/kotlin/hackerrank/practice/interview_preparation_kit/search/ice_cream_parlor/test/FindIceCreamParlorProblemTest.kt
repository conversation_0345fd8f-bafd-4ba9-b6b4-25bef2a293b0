package hackerrank.practice.interview_preparation_kit.search.ice_cream_parlor.test

import hackerrank.practice.util.verifyByFile
import hackerrank.practice.interview_preparation_kit.search.ice_cream_parlor.main as mainFunction

fun main() {
    case_0()
    case_1()
}

private fun case_0() {
    verifyByFile(
        caseNumber = 0,
        mainFunction = ::mainFunction
    )
}

private fun case_1() {
    verifyByFile(
        caseNumber = 1,
        mainFunction = ::mainFunction
    )
}
