package hackerrank.practice.interview_preparation_kit.search.ice_cream_parlor

import java.util.*

// Complete the whatFlavors function below.
fun whatFlavors(costs: Array<Int>, money: Int): Unit {
    val costMap = mutableMapOf<Int, Int>()
    for (index in costs.indices) {
        val cost = costs[index]
        val anotherCostWeNeed = money - cost
        if (costMap.containsKey(anotherCostWeNeed)) {
            println("${costMap[anotherCostWeNeed]} ${index + 1}")
        }

        costMap[cost] = index + 1
    }
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val t = scan.nextLine().trim().toInt()

    for (tItr in 1..t) {
        val money = scan.nextLine().trim().toInt()

        val n = scan.nextLine().trim().toInt()

        val cost = scan.nextLine().split(" ").map { it.trim().toInt() }.toTypedArray()

        whatFlavors(cost, money)
    }
}
