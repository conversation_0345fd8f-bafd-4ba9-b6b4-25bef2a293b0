package hackerrank.practice.interview_preparation_kit.search.minimum_time_required.test

import hackerrank.practice.interview_preparation_kit.search.minimum_time_required.main as mainFunction
import hackerrank.practice.util.sampleTemplate

fun main() {
    sample_0()
    sample_1()
    sample_2()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "2 5\n" +
                "2 3",
        expectedOutput = "6"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "3 10\n" +
                "1 3 4",
        expectedOutput = "7"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "3 12\n" +
                "4 5 6",
        expectedOutput = "20"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}