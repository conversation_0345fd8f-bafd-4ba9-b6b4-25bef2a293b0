package hackerrank.practice.interview_preparation_kit.search.minimum_time_required

import java.util.*
import kotlin.math.ceil
import kotlin.math.floor

// https://www.hackerrank.com/challenges/minimum-time-required/problem

// Complete the minTime function below.
fun minTime(machines: Array<Long>, goal: Long): Long {
    machines.sort()
    val minMachine = machines.first()
    val maxMachine = machines.last()

    var minDays = ceil(goal.toDouble() / machines.size.toDouble()) * minMachine.toDouble()
    var maxDays = ceil(goal.toDouble() / machines.size.toDouble()) * maxMachine.toDouble()
    var result = 0.0

    while (minDays < maxDays) {
        // m := floor((L + R) / 2)
        val midDays = floor((minDays + maxDays) / 2)
        var productions = 0L
        for (machine in machines) {
            productions += floor(midDays / machine).toLong()
        }
        if (productions < goal) {
            minDays = midDays + 1
        } else {
            // the objective is to find the smallest days
            maxDays = midDays
            result = midDays
        }
    }
    return result.toLong()
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val nGoal = scan.nextLine().split(" ")

    val n = nGoal[0].trim().toInt()

    val goal = nGoal[1].trim().toLong()

    val machines = scan.nextLine().split(" ").map { it.trim().toLong() }.toTypedArray()

    val ans = minTime(machines, goal)

    println(ans)
}
