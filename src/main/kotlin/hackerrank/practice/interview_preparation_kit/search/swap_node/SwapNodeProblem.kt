package hackerrank.practice.interview_preparation_kit.search.swap_node

import java.util.*

/*
 * Complete the swapNodes function below.
 */
fun swapNodes(indexes: Array<Array<Int>>, queries: Array<Int>): Array<Array<Int>> {
    val root = Node(value = 1)
    val queue = LinkedList<Node?>()
    queue.push(root)
    var valuesIndex = 0
    val numberOfNodes = indexes.size
    while (queue.isNotEmpty() && valuesIndex < numberOfNodes) {
        val parentNode = queue.poll()
        parentNode?.let {
            val values = indexes[valuesIndex++]
            val left = values[0]
            if (left == -1) {
                queue.offer(null)
            } else {
                it.left = Node(value = left)
                queue.offer(it.left)
            }
            val right = values[1]
            if (right == -1) {
                queue.offer(null)
            } else {
                it.right = Node(value = right)
                queue.offer(it.right)
            }
        }
    }

    val result = Array(queries.size) { Array(numberOfNodes) { 0 } }
    for ((resultIndex, query) in queries.withIndex()) {
        swap(root, query)
        val inOrderTraversal = inOrderTraversal(numberOfNodes, root)
        result[resultIndex] = inOrderTraversal
    }
    return result
}

private fun swap(node: Node, query: Int) {
    swapEveryKLevelUtil(node, 1, query)
}

fun swapEveryKLevelUtil(node: Node?, level: Int, k: Int) {
    // base case
    if (node == null ||
        (node.left == null && node.right == null)
    ) {
        return
    }

    // swap children nodes at kth level
    if (level % k == 0) {
        val temp = node.left
        node.left = node.right
        node.right = temp
    }

    // Recur for left and right subtrees
    swapEveryKLevelUtil(node.left, level + 1, k)
    swapEveryKLevelUtil(node.right, level + 1, k)
}

private fun inOrderTraversal(numberOfNodes: Int, node: Node): Array<Int> {
    val result = Array(numberOfNodes) { -1 }
    val stack = Stack<Node?>()
    var currentNode: Node? = node
    var resultIndex = 0
    while (currentNode != null || stack.isNotEmpty()) {
        while (currentNode != null) {
            stack.push(currentNode)
            currentNode = currentNode.left
        }
        // Current must be NULL at this point
        currentNode = stack.pop()
        currentNode?.let {
            result[resultIndex++] = it.value
            currentNode = it.right
        }
    }
    return result
}

data class Node(
    val value: Int,
    var left: Node? = null,
    var right: Node? = null
)

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val n = scan.nextLine().trim().toInt()

    val indexes = Array<Array<Int>>(n, { Array<Int>(2, { 0 }) })

    for (indexesRowItr in 0 until n) {
        indexes[indexesRowItr] = scan.nextLine().split(" ").map { it.trim().toInt() }.toTypedArray()
    }

    val queriesCount = scan.nextLine().trim().toInt()

    val queries = Array<Int>(queriesCount, { 0 })
    for (queriesItr in 0 until queriesCount) {
        val queriesItem = scan.nextLine().trim().toInt()
        queries[queriesItr] = queriesItem
    }

    val result = swapNodes(indexes, queries)

    println(result.map { it.joinToString(" ") }.joinToString("\n"))
}
