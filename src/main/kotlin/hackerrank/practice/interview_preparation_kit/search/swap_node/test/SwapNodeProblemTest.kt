package hackerrank.practice.interview_preparation_kit.search.swap_node.test

import hackerrank.practice.interview_preparation_kit.search.swap_node.main as mainFunction
import hackerrank.practice.util.sampleTemplate

fun main() {
    sample_0()
    sample_1()
    sample_2()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "3\n" +
                "2 3\n" +
                "-1 -1\n" +
                "-1 -1\n" +
                "2\n" +
                "1\n" +
                "1",
        expectedOutput = "3 1 2\n" +
                "2 1 3"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "5\n" +
                "2 3\n" +
                "-1 4\n" +
                "-1 5\n" +
                "-1 -1\n" +
                "-1 -1\n" +
                "1\n" +
                "2",
        expectedOutput = "4 2 1 5 3"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "11\n" +
                "2 3\n" +
                "4 -1\n" +
                "5 -1\n" +
                "6 -1\n" +
                "7 8\n" +
                "-1 9\n" +
                "-1 -1\n" +
                "10 11\n" +
                "-1 -1\n" +
                "-1 -1\n" +
                "-1 -1\n" +
                "2\n" +
                "2\n" +
                "4",
        expectedOutput = "2 9 6 4 1 3 7 5 11 8 10\n" +
                "2 6 9 4 1 3 7 5 10 8 11"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}
