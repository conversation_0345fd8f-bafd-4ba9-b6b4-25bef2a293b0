package hackerrank.practice.mathematics.number_theory.find_gcd_product.test

import hackerrank.practice.util.testTemplate

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_9()
    sample_16()
    sample_17()
    sample_18()
    sample_19()
}

private fun sample_1() {
    val inputs = "1 1"
    val expectedOutput = "1"
    testTemplate("sample_1", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_2() {
    val inputs = "2 2"
    val expectedOutput = "2"
    testTemplate("sample_2", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_3() {
    val inputs = "3 3"
    val expectedOutput = "6"
    testTemplate("sample_3", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_4() {
    val inputs = "4 4"
    val expectedOutput = "96"
    testTemplate("sample_4", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_9() {
    val inputs = "14555661 14127871"
    val expectedOutput = "970825132"
    testTemplate("sample_9", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_16() {
    val inputs = "65536 32722"
    val expectedOutput = "425873408"
    testTemplate("sample_16", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_17() {
    val inputs = "12121 12898"
    val expectedOutput = "322616442"
    testTemplate("sample_17", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_18() {
    val inputs = "4343 1289"
    val expectedOutput = "604629566"
    testTemplate("sample_18", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}

private fun sample_19() {
    val inputs = "1000 898"
    val expectedOutput = "403156183"
    testTemplate("sample_19", inputs, expectedOutput) { args ->
        hackerrank.practice.mathematics.number_theory.find_gcd_product.main(args)
    }
}
