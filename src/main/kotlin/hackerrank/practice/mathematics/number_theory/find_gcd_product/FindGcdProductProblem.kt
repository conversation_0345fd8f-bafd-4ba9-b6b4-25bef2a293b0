package hackerrank.practice.mathematics.number_theory.find_gcd_product

import java.util.*
import kotlin.math.min

/**
 * https://www.hackerrank.com/challenges/gcd-product/problem
 *
 * reference: https://github.com/derekhh/HackerRank/blob/master/gcd-product.cpp
 */
fun solve(n: Int, m: Int): Int {
    val (primeNumbers, numberOfPrimeNumbers) = findPrimeNumbers(n, m)

    val modulo = 1000000007 // 10^9+7
    var result: Long = 1
    for (i in 0 until numberOfPrimeNumbers) {
        val primeNumber = primeNumbers[i].toLong()
        var powerOfPrimeNumber: Long = 0
        var divisor = primeNumber
        while (n / divisor != 0L && m / divisor != 0L) {
            powerOfPrimeNumber += n / divisor * (m / divisor)
            divisor *= primeNumber
        }
        val factor = calculateFactor(primeNumber, powerOfPrimeNumber, modulo)
        result = result * factor % modulo
    }

    return result.toInt()
}

private fun findPrimeNumbers(n: Int, m: Int): PrimeNumbersAndNumberOfPrimeNumbers {
    val min = min(n, m)
    val isPrimes = BooleanArray(min + 1) { true }
    val primeNumbers = IntArray(min)
    isPrimes[0] = false
    isPrimes[1] = false
    var numberOfPrimeNumbers = 0
    for (number in 2..min) {
        if (isPrimes[number]) {
            for (productOfTheNumber in (number + number)..min step number) {
                isPrimes[productOfTheNumber] = false
            }
            primeNumbers[numberOfPrimeNumbers++] = number
        }
    }
    return PrimeNumbersAndNumberOfPrimeNumbers(
        primeNumbers = primeNumbers,
        numberOfPrimeNumbers = numberOfPrimeNumbers
    )
}

data class PrimeNumbersAndNumberOfPrimeNumbers(
    val primeNumbers: IntArray,
    val numberOfPrimeNumbers: Int
)

fun calculateFactor(primeNumber: Long, numberOfPrimeNumbers: Long, modulo: Int): Long {
    var loopIndicator = numberOfPrimeNumbers
    var factor: Long = 1
    var d = primeNumber
    while (loopIndicator != 0L) {
        if (loopIndicator and 1 != 0L) {
            factor = factor * d % modulo
        }
        d = d * d % modulo
        loopIndicator = loopIndicator shr 1
    }
    return factor
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val nm = scan.nextLine().split(" ")

    val n = nm[0].trim().toInt()

    val m = nm[1].trim().toInt()

    val result = solve(n, m)

    println(result)
}