package hackerrank.practice.data_structure.find_running_median

import java.util.*

/*
 * Complete the runningMedian function below.
 */
fun runningMedian(a: Array<Int>): Array<Double> {
    val bucketSize = a.size / 2 + 1
    val lowerBucket = PriorityQueue<Int>(bucketSize, Collections.reverseOrder())
    val upperBucket = PriorityQueue<Int>(bucketSize)

    val medians = Array(a.size) { 0.0 }
    a.forEachIndexed { index, value ->
        putValueIntoBuckets(lowerBucket, upperBucket, value)
        rebalanceBuckets(lowerBucket, upperBucket)

        val median = findMedian(lowerBucket, upperBucket)
        medians[index] = median
    }

    return medians
}

private fun putValueIntoBuckets(lowerBucket: PriorityQueue<Int>, upperBucket: PriorityQueue<Int>, value: Int) {
    if (lowerBucket.isEmpty() || value <= lowerBucket.peek()) {
        lowerBucket.add(value)
    } else {
        upperBucket.add(value)
    }
}

private fun rebalanceBuckets(lowerBucket: PriorityQueue<Int>, upperBucket: PriorityQueue<Int>) {
    val (smallerBucket, largerBucket) = findOrderedBucketBySize(lowerBucket, upperBucket)
    val bucketsAreUnbalanced = largerBucket.size - smallerBucket.size > 1
    if (bucketsAreUnbalanced) {
        smallerBucket.add(largerBucket.poll())
    }
}

private fun findMedian(lowerBucket: PriorityQueue<Int>, upperBucket: PriorityQueue<Int>): Double {
    val (smallerBucket, largerBucket) = findOrderedBucketBySize(lowerBucket, upperBucket)
    return if (smallerBucket.size == largerBucket.size) {
        (smallerBucket.peek() + largerBucket.peek()).toDouble() / 2
    } else {
        largerBucket.peek().toDouble()
    }
}

private fun findOrderedBucketBySize(lowerBucket: PriorityQueue<Int>, upperBucket: PriorityQueue<Int>): Buckets {
    val lowerBucketSize = lowerBucket.size
    val upperBucketSize = upperBucket.size
    val smallerBucket = if (lowerBucketSize <= upperBucketSize) lowerBucket else upperBucket
    val largerBucket = if (smallerBucket == lowerBucket) upperBucket else lowerBucket
    return Buckets(smallerBucket, largerBucket)
}

private data class Buckets(
    val smallerBucket: PriorityQueue<Int>,
    val largerBucket: PriorityQueue<Int>
)

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val aCount = scan.nextLine().trim().toInt()

    val a = Array(aCount) { 0 }
    for (aItr in 0 until aCount) {
        val aItem = scan.nextLine().trim().toInt()
        a[aItr] = aItem
    }

    val result = runningMedian(a)

    println(result.joinToString("\n"))
}
