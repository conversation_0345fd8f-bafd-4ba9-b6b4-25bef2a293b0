package hackerrank.practice.data_structure.find_running_median.test

import hackerrank.practice.util.testTemplate

fun main() {
    sample_0()
}

private fun sample_0() {
    testTemplate(
        caseName = "sample_0",
        inputs = "6\n" +
                "12\n" +
                "4\n" +
                "5\n" +
                "3\n" +
                "8\n" +
                "7",
        expectedOutput = "12.0\n" +
                "8.0\n" +
                "5.0\n" +
                "4.5\n" +
                "5.0\n" +
                "6.0"
    ) { args ->
        hackerrank.practice.data_structure.find_running_median.main(args)
    }
}
