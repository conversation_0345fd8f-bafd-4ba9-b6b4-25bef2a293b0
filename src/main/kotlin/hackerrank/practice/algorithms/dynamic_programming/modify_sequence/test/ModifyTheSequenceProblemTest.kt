package hackerrank.practice.algorithms.dynamic_programming.modify_sequence.test

import hackerrank.practice.util.sampleTemplate
import hackerrank.practice.util.verifyByFile
import hackerrank.practice.algorithms.dynamic_programming.modify_sequence.main as mainFunction

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    case_1()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "3\n" +
                "4 10 20",
        expectedOutput = "0"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "6\n" +
                "1 7 10 2 20 22",
        expectedOutput = "1"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "5\n" +
                "1 2 2 3 4",
        expectedOutput = "3"
    )
}

private fun sample_3() {
    sample(
        sampleNumber = 3,
        inputs = "5\n" +
                "1 7 3 4 5",
        expectedOutput = "1"
    )
}

private fun case_1() {
    verifyByFile(
        caseNumber = 1,
        mainFunction = ::mainFunction
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}

