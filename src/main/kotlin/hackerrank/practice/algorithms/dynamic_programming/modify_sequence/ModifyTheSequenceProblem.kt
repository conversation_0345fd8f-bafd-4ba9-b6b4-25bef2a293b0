package hackerrank.practice.algorithms.dynamic_programming.modify_sequence

import java.util.*

/*
 * Complete the modifySequence function below.
 *
 * https://www.hackerrank.com/challenges/modify-the-sequence/problem
 */
fun modifySequence(arr: Array<Int>): Int {
    /*
     * if the item is <= 0, it must be already in order
     * e.g.:
     * arr                       =  0  1  2  3 5
     * listToShowIfNumberInOrder = -1 -1 -1 -1 0
     */
    val listToShowIfNumberInOrder = arr.mapIndexed { index, number ->
        number - index - 1
    }
    // this list would store the unchanged numbers
    val unchangedNumbers = mutableListOf<Int>()
    for (value in listToShowIfNumberInOrder) {
        if (value < 0) {
            /*
             * less than 0 means the number is less than the min value it could be
             * e.g.: 0 1 2 3
             * The first value in listToShowIfNumberInOrder would be -1
             */
            continue
        }
        var lo = 0
        var hi = unchangedNumbers.size -1
        while (lo <= hi) {
            val mid = (lo + hi) / 2
            if (unchangedNumbers[mid] <= value) {
                lo = mid + 1
            } else {
                hi = mid - 1
            }
        }
        if (unchangedNumbers.size <= lo) {
            unchangedNumbers.add(value)
        } else {
            unchangedNumbers[lo] = value
        }
    }
    return arr.size - unchangedNumbers.size
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val arrCount = scan.nextLine().trim().toInt()

    val arr = scan.nextLine().split(" ").map { it.trim().toInt() }.toTypedArray()

    val result = modifySequence(arr)

    println(result)
}
