package hackerrank.practice.algorithms.dynamic_programming.coin_change.test

import hackerrank.practice.algorithms.dynamic_programming.coin_change.main as mainFunction
import hackerrank.practice.util.sampleTemplate

fun main() {
    sample_0()
    sample_1()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "4 3\n" +
                "1 2 3",
        expectedOutput = "4"
    )
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "10 4\n" +
                "2 5 3 6",
        expectedOutput = "5"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}