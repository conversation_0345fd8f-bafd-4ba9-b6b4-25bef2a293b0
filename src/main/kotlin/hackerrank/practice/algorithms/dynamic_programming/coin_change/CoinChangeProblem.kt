package hackerrank.practice.algorithms.dynamic_programming.coin_change

/*
 * https://www.hackerrank.com/challenges/coin-change/problem
 *
 * https://www.geeksforgeeks.org/coin-change-dp-7/
 *
 * Complete the 'getWays' function below.
 *
 * The function is expected to return a LONG_INTEGER.
 * The function accepts following parameters:
 *  1. INTEGER target
 *  2. LONG_INTEGER_ARRAY coins
 */

fun getWays(target: Int, coins: Array<Long>): Long {
    val ways = LongArray(target + 1) { 0 }
    // there must be one solution where we do not include any coin
    ways[0] = 1

    for (coin in coins) {
        // only need to consider the value >= coin, so start from the coin
        for (value in coin..target) {
            ways[value.toInt()] += ways[(value - coin).toInt()]
        }
    }

    return ways[target]
}

fun main(args: Array<String>) {
    val first_multiple_input = readLine()!!.trimEnd().split(" ")

    val n = first_multiple_input[0].toInt()

    val m = first_multiple_input[1].toInt()

    val c = readLine()!!.trimEnd().split(" ").map { it.toLong() }.toTypedArray()

    // Print the number of ways of making change for 'n' units using coins having the values given by 'c'

    val ways = getWays(n, c)

    println(ways)
}
