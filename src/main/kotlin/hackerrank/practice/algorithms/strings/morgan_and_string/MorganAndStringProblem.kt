package hackerrank.practice.algorithms.strings.morgan_and_string

import java.io.BufferedReader
import java.io.InputStreamReader


// https://www.hackerrank.com/challenges/morgan-and-a-string/problem
// Complete the morganAndString function below.
fun morganAndString(
    a: String,
    b: String
): String {
    val aLength = a.length
    val bLength = b.length

    var aIndex = 0
    var bIndex = 0
    var resultIndex = 0
    val result = CharArray(aLength + bLength)
    while (aIndex < aLength && bIndex < bLength) {
        val aChar = a[aIndex]
        val bChar = b[bIndex]
        if (aChar < bChar) {
            result[resultIndex++] = a[aIndex++]
        } else if (bChar < aChar) {
            result[resultIndex++] = b[bIndex++]
        } else {
            // this is the trick
            var aSuffixIndex = aIndex
            var bSuffixIndex = bIndex
            var charToCheck = aChar
            while (aSuffixIndex < aLength && bSuffixIndex < bLength) {
                if (a[aSuffixIndex] != b[bSuffixIndex]) {
                    break
                } else if (a[aSuffixIndex] > charToCheck) {
                    while (aIndex < aSuffixIndex) {
                        result[resultIndex++] = a[aIndex++]
                    }
                    while (bIndex < bSuffixIndex) {
                        result[resultIndex++] = b[bIndex++]
                    }
                    charToCheck = a[aSuffixIndex]
                }
                aSuffixIndex++
                bSuffixIndex++
            }
            if (aSuffixIndex == aLength) {
                result[resultIndex++] = b[bIndex++]
            } else if (bSuffixIndex == bLength) {
                result[resultIndex++] = a[aIndex++]
            } else {
                if (a[aSuffixIndex] < b[bSuffixIndex]) {
                    result[resultIndex++] = a[aIndex++]
                } else {
                    result[resultIndex++] = b[bIndex++]
                }
            }
        }
    }

    while (aIndex < aLength) {
        result[resultIndex++] = a[aIndex++]
    }
    while (bIndex < bLength) {
        result[resultIndex++] = b[bIndex++]
    }

    return String(result)
}

fun main(args: Array<String>) {
    val reader = BufferedReader(InputStreamReader(System.`in`))
    val t = reader.readLine().trim().toInt()
    for (tItr in 1..t) {
        val a = reader.readLine()
        val b = reader.readLine()
        val result = morganAndString(a, b)
        println(result)
    }
}
