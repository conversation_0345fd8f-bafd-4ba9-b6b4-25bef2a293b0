package hackerrank.practice.algorithms.strings.morgan_and_string.test

import hackerrank.practice.util.readAsText
import hackerrank.practice.util.testTemplate

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    case_1()
    case_4()
    case_9()
}

private fun sample_1() {
    verify(
        caseName = "sample_1",
        inputs = "1\n" +
                "ACA\n" +
                "BCF",
        expectedOutput = "ABCACF"
    )
}

private fun sample_2() {
    verify(
        caseName = "sample_2",
        inputs = "2\n" +
                "JACK\n" +
                "DANIEL\n" +
                "ABACABA\n" +
                "ABACABA",
        expectedOutput = "DAJACKNIEL\n" +
                "AABABACABACABA"
    )
}

private fun sample_3() {
    verify(
        caseName = "sample_3",
        inputs = "1\n" +
                "BAB\n" +
                "BAC",
        expectedOutput = "BABABC"
    )
}

// this one should cover all the cases
private fun sample_4() {
    verify(
        caseName = "sample_4",
        inputs = "6\n" +
                "DAD\n" +
                "DAD\n" +
                "ABCBA\n" +
                "BCBA\n" +
                "BAC\n" +
                "BAB\n" +
                "DAD\n" +
                "DABC\n" +
                "YZYYZYZYY\n" +
                "ZYYZYZYY\n" +
                "ZZYYZZZA\n" +
                "ZZYYZZZB",
        expectedOutput = "DADADD\n" +
                "ABBCBACBA\n" +
                "BABABC\n" +
                "DABCDAD\n" +
                "YZYYZYYZYZYYZYZYY\n" +
                "ZZYYZZYYZZZAZZZB"
    )
}

private fun case_1() {
    verifyByFile(
        caseNumber = 1
    )
}

private fun case_4() {
    verifyByFile(
        caseNumber = 4
    )
}

// ~10s
private fun case_9() {
    verifyByFile(
        caseNumber = 9
    )
}

private fun verifyByFile(caseNumber: Int) {
    val caseName = "case_$caseNumber"
    val inputs = "/hackerrank/practice/algorithms/strings/morgan_and_string/case-$caseNumber-inputs.txt".readAsText()
    val expectedOutput =
        "/hackerrank/practice/algorithms/strings/morgan_and_string/case-$caseNumber-expected-outputs.txt".readAsText()
    testTemplate(caseName, inputs, expectedOutput) { args ->
        hackerrank.practice.algorithms.strings.morgan_and_string.main(args)
    }
}

private fun verify(caseName: String, inputs: String, expectedOutput: String) {
    testTemplate(caseName, inputs, expectedOutput) { args ->
        hackerrank.practice.algorithms.strings.morgan_and_string.main(args)
    }
}
