def morganAndString(a, b):
    res=[]
    i=j=0
    na,nb=len(a),len(b)
    a,b=a+'z',b+'z'
    while i<na and j<nb:
        if a[i]<b[j]:
            res.append(a[i])
            i+=1
        elif b[j]<a[i]:
            res.append(b[j])
            j+=1
        elif a[i+1:]<b[j+1:]:
            res.append(a[i])
            i+=1
        else:
            res.append(b[j])
            j+=1
    return ''.join(res)+a[i:na]+b[j:nb]