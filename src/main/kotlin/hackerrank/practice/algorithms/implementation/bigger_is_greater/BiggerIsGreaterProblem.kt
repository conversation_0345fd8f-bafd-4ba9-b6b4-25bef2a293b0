package hackerrank.practice.algorithms.implementation.bigger_is_greater

import java.util.*

// Complete the biggerIsGreater function below.
fun biggerIsGreater(w: String): String {
    // https://www.nayuki.io/page/next-lexicographical-permutation-algorithm
    val seq = w.toCharArray()

    // Find longest non-increasing suffix
    var indexOfHeadOfLongestNonIncreasingSuffix = seq.lastIndex
    while (indexOfHeadOfLongestNonIncreasingSuffix > 0
        && seq[indexOfHeadOfLongestNonIncreasingSuffix - 1] >= seq[indexOfHeadOfLongestNonIncreasingSuffix]) {
        indexOfHeadOfLongestNonIncreasingSuffix--
    }

    // Are we at the last permutation already?
    if (indexOfHeadOfLongestNonIncreasingSuffix <= 0) {
        return "no answer"
    }

    // Find rightmost element that exceeds the pivot
    var indexOfRightmostSuccessorToPivotInSuffix = seq.lastIndex
    val pivotIndex = indexOfHeadOfLongestNonIncreasingSuffix - 1
    val pivotValue = seq[pivotIndex]
    while (seq[indexOfRightmostSuccessorToPivotInSuffix] <= pivotValue) {
        indexOfRightmostSuccessorToPivotInSuffix--
    }
    // Assertion: indexOfRightmostSuccessorToPivotInSuffix >= indexOfHeadOfLongestNonIncreasingSuffix

    // Swap the pivot with rightmostSuccessorToPivotInSuffix
    swap(seq, pivotIndex, indexOfRightmostSuccessorToPivotInSuffix)

    // Reverse the suffix
    var headIndexInSuffix = indexOfHeadOfLongestNonIncreasingSuffix
    var tailIndexInSuffix = seq.lastIndex
    while (headIndexInSuffix < tailIndexInSuffix) {
        swap(seq, headIndexInSuffix++, tailIndexInSuffix--)
    }

    // Successfully computed the next permutation
    return String(seq)
}

private fun swap(seq: CharArray, index1: Int, index2: Int) {
    val value1 = seq[index1]
    seq[index1] = seq[index2]
    seq[index2] = value1
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val T = scan.nextLine().trim().toInt()

    for (TItr in 1..T) {
        val w = scan.nextLine()

        val result = biggerIsGreater(w)

        println(result)
    }
}
