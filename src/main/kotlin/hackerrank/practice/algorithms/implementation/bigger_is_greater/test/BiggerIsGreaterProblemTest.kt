package hackerrank.practice.algorithms.implementation.bigger_is_greater.test

import hackerrank.practice.util.testTemplate

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "1\n" +
                "a",
        expectedOutput = "no answer"
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "1\n" +
                "aa",
        expectedOutput = "no answer"
    )
}

private fun sample_3() {
    sample(
        sampleNumber = 3,
        inputs = "1\n" +
                "ab",
        expectedOutput = "ba"
    )
}

private fun sample_4() {
    sample(
        sampleNumber = 4,
        inputs = "1\n" +
                "ba",
        expectedOutput = "no answer"
    )
}

private fun sample_5() {
    sample(
        sampleNumber = 5,
        inputs = "1\n" +
                "abc",
        expectedOutput = "acb"
    )
}

private fun sample_6() {
    sample(
        sampleNumber = 6,
        inputs = "1\n" +
                "abgcdab",
        expectedOutput = "abgcdba"
    )
}

private fun sample_7() {
    sample(
        sampleNumber = 7,
        inputs = "1\n" +
                "acb",
        expectedOutput = "bac"
    )
}

private fun sample_8() {
    sample(
        sampleNumber = 8,
        inputs = "1\n" +
                "abgcdba",
        expectedOutput = "abgdabc"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    verify(caseName = "sample_$sampleNumber", inputs = inputs, expectedOutput = expectedOutput)
}

private fun verify(caseName: String, inputs: String, expectedOutput: String) {
    testTemplate(caseName, inputs, expectedOutput) { args ->
        hackerrank.practice.algorithms.implementation.bigger_is_greater.main(args)
    }
}