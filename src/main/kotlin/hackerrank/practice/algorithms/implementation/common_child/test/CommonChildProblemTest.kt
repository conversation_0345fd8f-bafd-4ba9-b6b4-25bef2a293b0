package hackerrank.practice.algorithms.implementation.common_child.test

import hackerrank.practice.util.testTemplate

fun main() {
    case_0()
    case_1()
    case_2()
    case_3()
    case_4()
    case_5()
    case_6()
    sample_0()
    sample_1()
}

private fun case_0() {
    verify(
        caseName = "case_0",
        inputs = "ABCD\n" +
                "ABDC",
        expectedOutput = "3"
    )
}

private fun case_1() {
    verify(
        caseName = "case_1",
        inputs = "HARRY\n" +
                "SALLY",
        expectedOutput = "2"
    )
}

private fun case_2() {
    verify(
        caseName = "case_2",
        inputs = "AA\n" +
                "BB",
        expectedOutput = "0"
    )
}

private fun case_3() {
    verify(
        caseName = "case_3",
        inputs = "SHINCHAN\n" +
                "NOHARAAA",
        expectedOutput = "3"
    )
}

private fun case_4() {
    verify(
        caseName = "case_4",
        inputs = "ABCDEF\n" +
                "FBDAMN",
        expectedOutput = "2"
    )
}

private fun case_5() {
    verify(
        caseName = "case_5",
        inputs = "ABCDEFG\n" +
                "ABDEFGC",
        expectedOutput = "6"
    )
}

private fun case_6() {
    verify(
        caseName = "case_6",
        inputs = "GAC\n" +
                "AGCAT",
        expectedOutput = "2"
    )
}
private fun sample_0() {
    verify(
        caseName = "sample_0",
        inputs = "OUDFRMYMAW\n" +
                "AWHYFCCMQX",
        expectedOutput = "2"
    )
}

private fun sample_1() {
    verify(
        caseName = "sample_1",
        inputs = "WEWOUCUIDGCGTRMEZEPXZFEJWISRSBBSYXAYDFEJJDLEBVHHKS\n" +
                "FDAGCXGKCTKWNECHMRXZWMLRYUCOCZHJRRJBOAJOQJZZVUYXIC",
        expectedOutput = "15"
    )
}

private fun verify(caseName: String, inputs: String, expectedOutput: String) {
    testTemplate(caseName, inputs, expectedOutput) { args ->
        hackerrank.practice.algorithms.implementation.common_child.main(args)
    }
}

