package hackerrank.practice.algorithms.implementation.common_child

import java.util.*
import kotlin.math.max

// https://www.hackerrank.com/challenges/common-child/problem


// Complete the commonChild function below.
fun commonChild(s1: String, s2: String): Int {
    return findLcsLength(s1.toCharArray(), s2.toCharArray())
}

// https://en.wikipedia.org/wiki/Longest_common_subsequence_problem
private fun findLcsLength(x: CharArray, y: CharArray): Int {
    val m = x.size
    val n = y.size
    val c = Array(m + 1) { IntArray(n + 1) }
    for (i in 0..m) {
        c[i][0] = 0
    }
    for (j in 0..n) {
        c[0][j] = 0
    }
    for (i in 1..m) {
        for (j in 1..n) {
            if (x[i - 1] == y[j - 1]) {
                c[i][j] = c[i - 1][j - 1] + 1
            } else {
                c[i][j] = max(c[i][j - 1], c[i - 1][j])
            }
        }
    }
    return c[m][n]
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val s1 = scan.nextLine()

    val s2 = scan.nextLine()

    val result = commonChild(s1, s2)

    println(result)
}
