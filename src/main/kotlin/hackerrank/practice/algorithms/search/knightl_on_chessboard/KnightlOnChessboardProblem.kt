package hackerrank.practice.algorithms.search.knightl_on_chessboard

import java.util.*

// https://www.hackerrank.com/challenges/knightl-on-chessboard/problem
// 5 <= n <= 25
// Complete the knightlOnAChessboard function below.
fun knightlOnAChessboard(n: Int): Array<Array<Int>> {
    val result = Array(n - 1) { Array(n - 1) { -1 } }
    for (rowIndex in 0 until n - 1) {
        for (columnIndex in 0 until n - 1) {
            val i = rowIndex + 1
            val j = columnIndex + 1
            val minMoves = knightL(n, i, j)
            result[rowIndex][columnIndex] = minMoves
        }
    }
    return result
}

fun knightL(n: Int, i: Int, j: Int): Int {
    val minMoves = Array(n) { Array(n) { -1 } }

    val queue: Queue<Pair<Int, Int>> = LinkedList()
    minMoves[0][0] = 0
    queue.offer(Pair(0, 0))
    while (queue.isNotEmpty()) {
        val location = queue.poll()
        val rowIndex = location.second
        val columnIndex = location.first

        val offsets = arrayOf(
            Pair(i, j), Pair(i, j * -1), Pair(i * -1, j), Pair(i * -1, j * -1),
            Pair(j, i), Pair(j, i * -1), Pair(j * -1, i), Pair(j * -1, i * -1)
        )
        offsets.forEach {
            val rowOffset = it.second
            val columnOffset = it.first

            val newRowIndex = rowIndex + rowOffset
            val newColumnIndex = columnIndex + columnOffset

            if (newRowIndex in 0 until n
                && newColumnIndex in 0 until n
                && minMoves[newRowIndex][newColumnIndex] == -1
            ) {
                minMoves[newRowIndex][newColumnIndex] = minMoves[rowIndex][columnIndex] + 1
                queue.offer(Pair(newColumnIndex, newRowIndex))
            }
        }
    }
    return minMoves[n - 1][n - 1]
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val n = scan.nextLine().trim().toInt()

    val result = knightlOnAChessboard(n)

    println(result.map { it.joinToString(" ") }.joinToString("\n"))
}
