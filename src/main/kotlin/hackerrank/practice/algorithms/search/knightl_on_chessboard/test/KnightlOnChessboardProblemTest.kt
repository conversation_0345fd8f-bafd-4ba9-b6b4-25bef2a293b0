package hackerrank.practice.algorithms.search.knightl_on_chessboard.test

import hackerrank.practice.algorithms.search.knightl_on_chessboard.main as mainFunction
import hackerrank.practice.util.sampleTemplate

fun main() {
    sample_0()
}

private fun sample_0() {
    sample(
        sampleNumber = 0,
        inputs = "5",
        expectedOutput = "4 4 2 8\n" +
                "4 2 4 4\n" +
                "2 4 -1 -1\n" +
                "8 4 -1 1"
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}
