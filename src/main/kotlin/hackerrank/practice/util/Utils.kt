package hackerrank.practice.util

import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.PrintStream
import java.util.concurrent.TimeUnit

fun sampleTemplate(
    sampleNumber: Int,
    inputs: String,
    expectedOutput: String,
    mainFunction: (args: Array<String>) -> Unit
) {
    verify(
        caseName = "sample_$sampleNumber",
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = mainFunction
    )
}

fun verifyByFile(caseNumber: Int, mainFunction: (args: Array<String>) -> Unit) {
    val caseName = "case_$caseNumber"
    val dir = "/" + mainFunction.javaClass.packageName.removeSuffix(".test").replace(".", "/")
    val inputs = "$dir/case-$caseNumber-inputs.txt".readAsText()
    val expectedOutput = "$dir/case-$caseNumber-expected-outputs.txt".readAsText()
    testTemplate(caseName, inputs, expectedOutput, mainFunction)
}

fun setToSystemIn(input: String) = System.setIn(ByteArrayInputStream(input.toByteArray(Charsets.UTF_8)))

fun testTemplate(
    caseName: String,
    inputs: String,
    expectedOutput: String,
    mainFunction: (args: Array<String>) -> Unit
) {
    setToSystemIn(inputs)

    val originalPrintStream = System.out
    val outputStream = ByteArrayOutputStream()
    System.setOut(PrintStream(outputStream))

    val startTime = System.nanoTime()
    mainFunction(emptyArray())
    val timeUsed = System.nanoTime() - startTime
    val timeUsedInMilliseconds = TimeUnit.MILLISECONDS.convert(timeUsed, TimeUnit.NANOSECONDS).toDouble()

    val actualOutputs = outputStream.toString()
        .split(System.lineSeparator())
        .filter { it.isNotBlank() }
    outputStream.close()

    System.setOut(originalPrintStream)
    val expectedOutputs = expectedOutput.split("\n")
    if (actualOutputs.size != expectedOutputs.size) {
        println("\"$caseName\" fails: actualOutputs.size != expectedOutput.size where frequencies.size = ${actualOutputs.size}, expectedOutput.size = ${expectedOutputs.size}")
        return
    }

    val failedResults = mutableListOf<FailResult>()
    actualOutputs.forEachIndexed { index, actual ->
        val expectedFrequency = expectedOutputs[index]
        if (actual != expectedFrequency) failedResults.add(
            FailResult(
                index = index,
                actualValue = actual,
                expectedValue = expectedFrequency
            )
        )
    }
    val hasFailResult = failedResults.isNotEmpty()
    if (hasFailResult) {
        println("$caseName fails")
        failedResults.forEach {
            println(it)
        }
    } else {
        when {
            timeUsedInMilliseconds < 1000 -> println("\"$caseName\" is passed and used $timeUsedInMilliseconds ms")
            timeUsedInMilliseconds < 60000 -> println("\"$caseName\" is passed and used ${timeUsedInMilliseconds / 1000} s")
            else -> println("\"$caseName\" is passed and used ${timeUsedInMilliseconds / 1000 / 60} min")
        }

    }
}

fun String.readAsText(): String {
    return object {}.javaClass.getResource(this).readText(Charsets.UTF_8)
}

private fun verify(
    caseName: String,
    inputs: String,
    expectedOutput: String,
    mainFunction: (args: Array<String>) -> Unit
) {
    testTemplate(caseName, inputs, expectedOutput, mainFunction)
}

private data class FailResult(
    val index: Int,
    val actualValue: String,
    val expectedValue: String
) {
    override fun toString(): String {
        val sb = StringBuilder()
            .append("index=").append(index).append("\n")
            .append("actualValue=  ").append(actualValue).append("\n")
            .append("expectedValue=").append(expectedValue).append("\n")

        return sb.toString()
    }
}
