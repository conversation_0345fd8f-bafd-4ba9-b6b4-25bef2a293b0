import java.util.ArrayList
import java.util.TreeMap

fun main() {
    // Test case from problem description
    val paths1 = listOf(
        listOf("a"),
        listOf("c"),
        listOf("a", "x"),
        listOf("c", "x"),
        listOf("b"),
        listOf("c", "x", "y"),
        listOf("a", "x", "y")
    )
    println("Test 1 result: ${deleteDuplicateFolder(paths1)}")

    // Original test case
    val paths2 = listOf(
        listOf("a"),
        listOf("c"),
        listOf("d"),
        listOf("a", "b"),
        listOf("c", "b"),
        listOf("d", "a"),
        listOf("d", "a", "e"),
        listOf("d", "a", "f"),
    )
    println("Test 2 result: ${deleteDuplicateFolder(paths2)}")
}


    fun deleteDuplicateFolder(paths: List<List<String>>): List<List<String>> {
        // time: O(n*len(path) + numberOfTrieNodes*hash(path))
        // space: O(n*len(path))
        val root = buildTrie(paths)

        // Map to store subtree hash -> list of nodes with that hash
        val subtreeToNodes = mutableMapOf<String, MutableList<TrieNode>>()
        buildSubtreeHash(root, subtreeToNodes)

        // Mark nodes for deletion if they have duplicate subtrees
        markDeleted(subtreeToNodes)

        // Build result paths
        val result = mutableListOf<List<String>>()
        val path = mutableListOf<String>()
        buildPaths(root, path, result)

        return result
    }

    private fun buildTrie(paths: List<List<String>>): TrieNode {
        val root = TrieNode("")
        for (path in paths) {
            var node = root
            for (folder in path) {
                node = node.children.computeIfAbsent(folder) { TrieNode(folder) }
            }
        }
        return root
    }

    private fun buildSubtreeHash(node: TrieNode, subtreeToNodes: MutableMap<String, MutableList<TrieNode>>): String {
        // Build hash for current subtree
        val sb = StringBuilder("(")

        // Process children in sorted order for consistent hashing
        for ((name, child) in node.children) {
            sb.append(name)
            sb.append(buildSubtreeHash(child, subtreeToNodes))
        }
        sb.append(")")

        val subtreeHash = sb.toString()
        node.hash = subtreeHash

        // Only add non-empty subtrees (folders with children) to the map
        if (subtreeHash != "()") {
            subtreeToNodes.computeIfAbsent(subtreeHash) { mutableListOf() }.add(node)
        }

        return subtreeHash
    }

    private fun markDeleted(subtreeToNodes: MutableMap<String, MutableList<TrieNode>>) {
        for (nodes in subtreeToNodes.values) {
            if (nodes.size > 1) {
                for (node in nodes) {
                    node.deleted = true
                }
            }
        }
    }

    private fun buildPaths(node: TrieNode, path: MutableList<String>, result: MutableList<List<String>>) {
        // Process children in sorted order
        for ((name, child) in node.children) {
            if (!child.deleted) {
                path.add(name)
                result.add(ArrayList(path))
                buildPaths(child, path, result)
                path.removeAt(path.size - 1)
            }
        }
    }

    private data class TrieNode(val name: String) {
        val children = TreeMap<String, TrieNode>()
        var hash = ""
        var deleted = false
    }

