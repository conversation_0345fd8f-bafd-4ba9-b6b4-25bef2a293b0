package brilliant.greedy_algorithms.help_sally

fun main() {
    val input = listOf(
        listOf("S", ".", ".", ".", ".", ".", ".", "#", ".", "."),
        listOf("#", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", "D", "#", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", "#", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", ".", ".", "."),
        listOf(".", ".", ".", ".", ".", ".", ".", "#", ".", ".")
    )

    printMap(input)

    val sallyLocation = findLocation(input, sally)
    println("sallyLocation: $sallyLocation")
    val daddyLocation = findLocation(input, dad)
    println("daddyLocation: $daddyLocation")

    val shortestDistance = findShortestDistance(input, sallyLocation, daddyLocation)
    println("shortestDistance: $shortestDistance")
}