package brilliant.greedy_algorithms.help_sally

import hackerrank.practice.util.readAsText

/**
 * https://brilliant.org/problems/help-sally/
 */

const val wall = "#"
const val sally = "S"
const val dad = "D"

enum class Direction {
    UP, RIGHT, DOWN, LEFT
}

fun main() {
    helpSally()
}

private fun helpSally() {
    val inputs = "/brilliant/greedy_algorithms/help_sally/input.txt".readAsText().split("--------------------")
    var shortestDistances = 0
    inputs
        .filter { it.isNotEmpty() } /* the last list is always empty list */
        .forEach {
            println()
            val input = stringToArray(it)

            printMap(input)

            val sallyLocation = findLocation(input, sally)
            println("sallyLocation: $sallyLocation")
            val daddyLocation = findLocation(input, dad)
            println("daddyLocation: $daddyLocation")

            val shortestDistance = findShortestDistance(input, sallyLocation, daddyLocation)
            println("shortestDistance: $shortestDistance")

            shortestDistances += shortestDistance
        }
    println("shortestDistances: $shortestDistances")
}

private fun stringToArray(inputInString: String): List<List<String>> {
    return inputInString
        .split("\n").map { line ->
            line.toCharArray().map { c -> c.toString() }
        }
        .filter { it.isNotEmpty() }
}

fun printMap(s: List<List<String>>) {
    s.forEach { row ->
        row.forEach { column -> print(column) }
        println()
    }
}

fun findLocation(input: List<List<String>>, targetSymbol: String): Pair<Int, Int> {
    for (rowIndex in input.indices) {
        val row = input[rowIndex]
        for (columnIndex in row.indices) {
            val symbol = input[rowIndex][columnIndex]
            if (symbol == targetSymbol) {
                return Pair(rowIndex, columnIndex)
            }
        }
    }
    throw IllegalStateException("cannot find $targetSymbol")
}

/**
 * we first need to start from the sally location
 *
 * then we use BFS to find the shortest distances from the node to position where sally stops
 */
fun findShortestDistance(
    input: List<List<String>>, sallyLocation: Pair<Int, Int>,
    daddyLocation: Pair<Int, Int>
): Int {
    val shortestDistances = Array(input.size) {
        IntArray(input[0].size) { Int.MAX_VALUE }
    }
    shortestDistances[sallyLocation.first][sallyLocation.second] = 0

    val visitedNodes = Array(input.size) {
        BooleanArray(input[0].size) { false }
    }

    repeat(input.size * input[0].size) {
        val nodeLocationWithMinShortestDistanceFromSource =
            findNodeLocationWithMinShortestDistanceFromSource(shortestDistances, visitedNodes)
        if (nodeLocationWithMinShortestDistanceFromSource == daddyLocation) {
            return@repeat
        }
        val rowIndex = nodeLocationWithMinShortestDistanceFromSource.first
        val columnIndex = nodeLocationWithMinShortestDistanceFromSource.second
        visitedNodes[rowIndex][columnIndex] = true
        val currentShortestDistance = shortestDistances[rowIndex][columnIndex]

        val upperDestination = findDestination(input, nodeLocationWithMinShortestDistanceFromSource, Direction.UP)
        val rightDestination = findDestination(input, nodeLocationWithMinShortestDistanceFromSource, Direction.RIGHT)
        val bottomDestination = findDestination(input, nodeLocationWithMinShortestDistanceFromSource, Direction.DOWN)
        val leftDestination = findDestination(input, nodeLocationWithMinShortestDistanceFromSource, Direction.LEFT)

        updateShortestDistance(
            upperDestination,
            currentShortestDistance,
            shortestDistances
        ) { destinationRowIndex: Int, destinationColumnIndex: Int -> rowIndex - destinationRowIndex }

        updateShortestDistance(
            rightDestination,
            currentShortestDistance,
            shortestDistances
        ) { destinationRowIndex: Int, destinationColumnIndex: Int -> destinationColumnIndex - columnIndex }


        updateShortestDistance(
            bottomDestination,
            currentShortestDistance,
            shortestDistances
        ) { destinationRowIndex: Int, destinationColumnIndex: Int -> destinationRowIndex - rowIndex }

        updateShortestDistance(
            leftDestination,
            currentShortestDistance,
            shortestDistances
        ) { destinationRowIndex: Int, destinationColumnIndex: Int -> columnIndex - destinationColumnIndex }
    }

    return shortestDistances[daddyLocation.first][daddyLocation.second]
}

fun findNodeLocationWithMinShortestDistanceFromSource(
    shortestDistances: Array<IntArray>,
    visitedNodes: Array<BooleanArray>
): Pair<Int, Int> {
    var shortestDistance = Int.MAX_VALUE
    var node: Pair<Int, Int>? = null
    for (rowIndex in shortestDistances.indices) {
        val row = shortestDistances[rowIndex]
        for (columnIndex in row.indices) {
            val notYetVisited = !visitedNodes[rowIndex][columnIndex]
            if (notYetVisited) {
                val distance = shortestDistances[rowIndex][columnIndex]
                if (distance < shortestDistance) {
                    shortestDistance = distance
                    node = Pair(rowIndex, columnIndex)
                }
            }
        }
    }
    return node ?: throw IllegalStateException("cannot find the next node")
}

fun findDestination(
    input: List<List<String>>,
    nodeLocationWithMinShortestDistanceFromSource: Pair<Int, Int>,
    direction: Direction
): Pair<Int, Int>? {
    val rowIndex = nodeLocationWithMinShortestDistanceFromSource.first
    val columnIndex = nodeLocationWithMinShortestDistanceFromSource.second

    return when (direction) {
        Direction.UP -> {
            var destinationRowIndex = rowIndex - 1
            while (destinationRowIndex >= 0 && input[destinationRowIndex][columnIndex] != wall) {
                destinationRowIndex--
            }
            if (destinationRowIndex == rowIndex) {
                null
            } else {
                Pair(destinationRowIndex + 1, columnIndex)
            }
        }
        Direction.RIGHT -> {
            var destinationColumnIndex = columnIndex + 1
            while (destinationColumnIndex < input[0].size && input[rowIndex][destinationColumnIndex] != wall) {
                destinationColumnIndex++
            }
            if (destinationColumnIndex == columnIndex) {
                null
            } else {
                Pair(rowIndex, destinationColumnIndex - 1)
            }
        }
        Direction.DOWN -> {
            var destinationRowIndex = rowIndex + 1
            while (destinationRowIndex < input.size && input[destinationRowIndex][columnIndex] != wall) {
                destinationRowIndex++
            }
            if (destinationRowIndex == rowIndex) {
                null
            } else {
                Pair(destinationRowIndex - 1, columnIndex)
            }
        }
        Direction.LEFT -> {
            var destinationColumnIndex = columnIndex - 1
            while (destinationColumnIndex >= 0 && input[rowIndex][destinationColumnIndex] != wall) {
                destinationColumnIndex--
            }
            if (destinationColumnIndex == columnIndex) {
                null
            } else {
                Pair(rowIndex, destinationColumnIndex + 1)
            }
        }
    }
}

fun updateShortestDistance(
    destination: Pair<Int, Int>?,
    currentShortestDistance: Int,
    shortestDistances: Array<IntArray>,
    findDistance: (Int, Int) -> Int
) {
    destination?.let {
        val destinationRowIndex = it.first
        val destinationColumnIndex = it.second

        val newShortestDistance = currentShortestDistance + findDistance(destinationRowIndex, destinationColumnIndex)
        if (newShortestDistance < shortestDistances[destinationRowIndex][destinationColumnIndex]) {
            shortestDistances[destinationRowIndex][destinationColumnIndex] = newShortestDistance
        }
    }
}
