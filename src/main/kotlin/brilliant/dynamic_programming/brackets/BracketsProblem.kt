package brilliant.dynamic_programming.brackets

import hackerrank.practice.util.readAsText
import java.lang.Integer.max

/**
 * https://brilliant.org/problems/inoi-2016-brackets/
 */
fun main() {
    sample()
//    realProblem()
}

private fun realProblem() {
    val inputs = "/brilliant/dynamic_programming/brackets/input.txt".readAsText().split(" ")
        .map { it.toInt() }
    val N = inputs[0]
    val k = inputs[1]
    val V = inputs.subList(2, N + 2)
    val B = inputs.subList(N + 2, 2 * N + 2)

    val maxSum = findMaxSum(N, k, V, B)
    println(maxSum)
    if (maxSum != 351549626) throw IllegalStateException("expecting $maxSum to be equal to 351549626")
}

private fun sample() {
    val N = 6
    val k = 3
    val V = listOf(4, 5, -2, 1, 1, 6)
    val B = listOf(1, 3, 4, 2, 5, 6)

    val maxSum = findMaxSum(N, k, V, B)
    println(maxSum)
}

fun findMaxSum(N: Int, k: Int, V: List<Int>, B: List<Int>): Int {
    // sumCache[start][end] (inclusive)
    val sumCache = Array(N) { IntArray(N) { 0 } }

    /*
     * the loop would be like
     * 0 to 1 (width = 1)
     * 1 to 2
     * 2 to 3
     * ...
     * 0 to 2 (width = 2)
     * ...
     * 0 to 5 (width = 5)
     */
    for (bracketWidth in 1 until N) {
        for (startIndex in 0 until N - bracketWidth) {
            val endIndex = startIndex + bracketWidth
//            println("start: $startIndex, end: $endIndex")
            // is there a valid bracket surrounding the interval [startIndex, endIndex] ?
            val canFormCompleteBracket = B[startIndex] + k == B[endIndex]
            var sumOfBrackets = if (canFormCompleteBracket) {
                V[startIndex] + V[endIndex] + sumCache[startIndex + 1][endIndex - 1]
            } else {
                0
            }
            // searching the best way to cut the interval [startIndex, endIndex] in two smaller optimal subsequences
            for (cut in startIndex until endIndex) {
                val firstHalf = sumCache[startIndex][cut]
                val secondHalf = sumCache[cut + 1][endIndex]
                val sumInsideBracket = firstHalf + secondHalf
                sumOfBrackets = max(sumOfBrackets, sumInsideBracket)
            }
            sumCache[startIndex][endIndex] = sumOfBrackets
        }
    }
    return sumCache[0][N - 1]
}


