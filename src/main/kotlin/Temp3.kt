import leetcode.toMatrix

fun main() {
    println(numDecodings("1201234"))
}

fun numDecodings(s: String): Int {
    // time: O(n)
    // space: O(n)
    val dp = IntArray(s.length) { -1 }
    return count(s, 0, dp)
}

private fun count(s: String, index: Int, dp: IntArray): Int {
    if (index == s.length) return 1
    if (s[index] == '0') return 0
    if (dp[index] != -1) return dp[index]
    val takeOneLetter = count(s, index + 1, dp)
    val takeTwoLetter = if (index + 1 < s.length) {
        val num = s.substring(index, index + 2).toInt()
        if (num in 1..26) {
            count(s, index + 2, dp)
        } else {
            0
        }
    } else {
        0
    }
    dp[index] = takeOneLetter + takeTwoLetter
    return dp[index]
}
