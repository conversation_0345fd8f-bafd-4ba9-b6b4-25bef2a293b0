import leetcode.printListNode
import leetcode.toArrayOfCharArray
import leetcode.toMatrix
import java.util.LinkedList

// =22011*14858*14858*14858*14858*3289
fun main() {
    val board = "[[\"5\",\"3\",\".\",\".\",\"7\",\".\",\".\",\".\",\".\"],[\"6\",\".\",\".\",\"1\",\"9\",\"5\",\".\",\".\",\".\"],[\".\",\"9\",\"8\",\".\",\".\",\".\",\".\",\"6\",\".\"],[\"8\",\".\",\".\",\".\",\"6\",\".\",\".\",\".\",\"3\"],[\"4\",\".\",\".\",\"8\",\".\",\"3\",\".\",\".\",\"1\"],[\"7\",\".\",\".\",\".\",\"2\",\".\",\".\",\".\",\"6\"],[\".\",\"6\",\".\",\".\",\".\",\".\",\"2\",\"8\",\".\"],[\".\",\".\",\".\",\"4\",\"1\",\"9\",\".\",\".\",\"5\"],[\".\",\".\",\".\",\".\",\"8\",\".\",\".\",\"7\",\"9\"]]".toArrayOfCharArray()
    solveSudoku(board)
}

fun solveSudoku(board: Array<CharArray>): Unit {
    val n = board.size
    val rows = Array(n) { IntArray(10) }
    val cols = Array(n) { IntArray(10) }
    val boxes = Array(n) { IntArray(10) }
    for (i in 0 until n) {
        for (j in 0 until n) {
            if (board[i][j] != '.') {
                rows[i][board[i][j] - '0']++
            }
        }
    }
    for (j in 0 until n) {
        for (i in 0 until n) {
            if (board[i][j] != '.') {
                rows[i][board[i][j] - '0']++
            }
        }
    }
}
