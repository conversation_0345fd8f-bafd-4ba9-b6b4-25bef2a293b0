package others.find_best_average_score.test

import others.find_best_average_score.StudentRecord
import others.find_best_average_score.findBestAverageScore
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_1() {
    verify(
        studentRecords = arrayOf(
            StudentRecord(name = "jerry", score = 65),
            StudentRecord(name = "bob", score = 91),
            StudentRecord(name = "<PERSON>", score = 83)
        ),
        expectedStudentWithBestAverageScore = "bob"
    )
}

private fun sample_2() {
    verify(
        studentRecords = arrayOf(
            StudentRecord(name = "jerry", score = 65),
            StudentRecord(name = "bob", score = 50),
            Student<PERSON><PERSON>ord(name = "<PERSON>", score = 10),
            StudentRecord(name = "jerry", score = 60),
            StudentRecord(name = "<PERSON>", score = 0)
        ),
        expectedStudentWithBestAverageScore = "jerry"
    )
}

private fun sample_3() {
    verify(
        studentRecords = emptyArray(),
        expectedStudentWithBestAverageScore = null
    )
}

private fun sample_4() {
    verify(
        studentRecords = arrayOf(
            StudentRecord(name = "<PERSON>", score = 65),
            StudentRecord(name = "jerry", score = 65),
            StudentRecord(name = "bob", score = 50),
            StudentRecord(name = "jerry", score = 60),
            StudentRecord(name = "Eric", score = 60)
        ),
        expectedStudentWithBestAverageScore = "eric"
    )
}

private fun sample_5() {
    verify(
        studentRecords = arrayOf(
            StudentRecord(name = "Eric", score = 65),
            StudentRecord(name = "jerry", score = 65),
            StudentRecord(name = "bob", score = 50),
            StudentRecord(name = "Jerry", score = 70)
        ),
        expectedStudentWithBestAverageScore = "jerry"
    )
}

private fun verify(studentRecords: Array<StudentRecord>, expectedStudentWithBestAverageScore: String?) {
    val studentWithBestAverageScore = findBestAverageScore(studentRecords)
    assertEquals(expectedStudentWithBestAverageScore, studentWithBestAverageScore)
}