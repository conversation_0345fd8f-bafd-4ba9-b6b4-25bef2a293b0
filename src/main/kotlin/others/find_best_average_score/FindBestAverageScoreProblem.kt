package others.find_best_average_score

/**
 * given an array scores [][] = {“jerry”,”65”},{“bob”,”91”}, {“jerry”,”23”}, {<PERSON><PERSON>,”83”}} Find the student with highest average score
 */
fun findBestAverageScore(scores: Array<StudentRecord>): String? {
    return scores
        .groupBy { it.name.lowercase() }
        .maxBy { entry: Map.Entry<String, List<StudentRecord>> -> entry.value.map { it.score }.toIntArray().average() }
        ?.key
}

data class StudentRecord(
    val name: String,
    val score: Int
)