package others.maximum_sum_path_in_a_matrix_from_top_to_bottom

/**
 * Consider a n*n matrix. Suppose each cell in the matrix has a value assigned.
 * We can go from each cell in row i to a diagonally higher cell in row i+1 only [i.e from cell(i, j)
 * to cell(i+1, j-1) and cell(i+1, j+1) only].
 * Find the path from the top row to the bottom row following the aforementioned condition such that the maximum sum is obtained.
 */
fun findMaximumSumPathInAMatrixFromTopToBottom(matrix: Array<Array<Int>>): Int {
    return if (matrix.isEmpty() || matrix.all { it.isEmpty() }) {
        0
    } else {
        val valueMatrix = matrix.map { Array(it.size) { 0 } }.toTypedArray()
        for (rowIndex in matrix.indices) {
            val isFirstRow = rowIndex == 0
            val row = matrix[rowIndex]
            if (isFirstRow) {
                valueMatrix[rowIndex] = row.clone()
            } else {
                for (columnIndex in row.indices) {
                    val currentValue = row[columnIndex]
                    val upperLeftValue = if (columnIndex - 1 >= 0) {
                        valueMatrix[rowIndex - 1][columnIndex - 1]
                    } else {
                        0
                    }

                    val upperRightValue = if (columnIndex + 1 < row.size) {
                        valueMatrix[rowIndex - 1][columnIndex + 1]
                    } else {
                        0
                    }

                    val largerUpperValue = arrayOf(upperLeftValue, upperRightValue).max()!!
                    valueMatrix[rowIndex][columnIndex] = currentValue + largerUpperValue
                }
            }
        }

        val lastRowOfValueMatrix = valueMatrix.last()
        return lastRowOfValueMatrix.max()!!
    }
}