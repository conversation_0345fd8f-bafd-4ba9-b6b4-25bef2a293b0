package others.maximum_sum_path_in_a_matrix_from_top_to_bottom.test

import others.maximum_sum_path_in_a_matrix_from_top_to_bottom.findMaximumSumPathInAMatrixFromTopToBottom
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
}

private fun sample_1() {
    verify(
        matrix = arrayOf(
            arrayOf()
        ),
        expected = 0
    )
}

private fun sample_2() {
    verify(
        matrix = arrayOf(
            arrayOf(5)
        ),
        expected = 5
    )
}

private fun sample_3() {
    verify(
        matrix = arrayOf(
            arrayOf(5, 10)
        ),
        expected = 10
    )
}

private fun sample_4() {
    verify(
        matrix = arrayOf(
            arrayOf(10, 5),
            arrayOf(15, 20)
        ),
        expected = 30
    )
}

private fun sample_5() {
    verify(
        matrix = arrayOf(
            arrayOf(10, 5, 0),
            arrayOf(15, 20, 2),
            arrayOf(7, 0, 1)
        ),
        expected = 37
    )
}

private fun sample_6() {
    verify(
        matrix = arrayOf(
            arrayOf(5, 6, 1, 7),
            arrayOf(-2, 10, 8, -1),
            arrayOf(3, -7, -9, 11),
            arrayOf(12, -4, 2, 6)
        ),
        expected = 28
    )
}

private fun verify(matrix: Array<Array<Int>>, expected: Int) {
    val actual = findMaximumSumPathInAMatrixFromTopToBottom(matrix)
    assertEquals(expected, actual)
}