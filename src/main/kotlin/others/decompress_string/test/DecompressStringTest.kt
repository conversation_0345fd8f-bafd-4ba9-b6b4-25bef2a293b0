package others.decompress_string.test

import others.decompress_string.decompressString
import kotlin.test.assertEquals

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_0() {
    val input = "abc"
    val output = decompressString(input)
    val expectedOutput = "abc"
    assertEquals(expectedOutput, output)
}

private fun sample_1() {
    val input = "a2[b]"
    val output = decompressString(input)
    val expectedOutput = "abb"
    assertEquals(expectedOutput, output)
}

private fun sample_2() {
    val input = "a2[b3[c]]"
    val output = decompressString(input)
    val expectedOutput = "abcccbccc"
    assertEquals(expectedOutput, output)
}

private fun sample_3() {
    val input = "a3[b2[c1[d]]]e"
    val output = decompressString(input)
    val expectedOutput = "abcdcdbcdcdbcdcde"
    assertEquals(expectedOutput, output)
}

private fun sample_4() {
    val input = "a2[bc]"
    val output = decompressString(input)
    val expectedOutput = "abcbc"
    assertEquals(expectedOutput, output)
}

private fun sample_5() {
    val input = "a0[b]"
    val output = decompressString(input)
    val expectedOutput = "a"
    assertEquals(expectedOutput, output)
}
