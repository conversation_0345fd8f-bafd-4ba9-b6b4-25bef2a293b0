package others.decompress_string

import java.util.*

/**
 * Given a compressed string in which a number followed by [] indicate
 * how many times those characters occur, decompress the string
 * Eg. : a3[b2[c1[d]]]e will be decompressed as abcdcdbcdcdbcdcde.
 */

private val nonCharacters = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '[', ']')

fun decompressString(input: String): String {
    val stack = Stack<String>()
    val result = StringBuilder()
    for (character in input) {
        if (character == ']') {
            // for this case stack must not be empty
            var tmpCharacter = stack.pop()
            val charactersInsideBracketInReverseOrder = mutableListOf<String>()
            while (tmpCharacter != "[") {
                charactersInsideBracketInReverseOrder.add(tmpCharacter)
                tmpCharacter = stack.pop()
            }
            val stringBuilderInsideBracket = StringBuilder()
            for (index in charactersInsideBracketInReverseOrder.lastIndex downTo 0) {
                stringBuilderInsideBracket.append(charactersInsideBracketInReverseOrder[index])
            }

            val number = stack.pop().toInt()
            val decompressedStringBuilder = StringBuilder()
            repeat(number) {
                decompressedStringBuilder.append(stringBuilderInsideBracket)
            }

            if (stack.isEmpty()) {
                result.append(decompressedStringBuilder)
            } else {
                stack.push(decompressedStringBuilder.toString())
            }
        } else {
            val isCharacter = !nonCharacters.contains(character)
            if (stack.isEmpty() && isCharacter) {
                result.append(character)
            } else {
                stack.push(character.toString())
            }
        }
    }

    return result.toString()
}