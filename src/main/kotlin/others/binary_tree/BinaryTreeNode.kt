package others.binary_tree

import kotlin.math.absoluteValue
import kotlin.math.max

abstract class BinaryTreeNode<Node : BinaryTreeNode<Node, Value>, Value>(
    open val value: Value,
    open var leftNode: Node? = null,
    open var rightNode: Node? = null
) {
    abstract fun insert(value: Int)

    abstract fun contains(value: Int): Boolean

    abstract fun printInorder()

    fun isBalanced(): Boolean {
        val leftNodeHeight = leftNodeHeight()
        val isLeftNodeBalanced = leftNode?.isBalanced() ?: true
        val rightNodeHeight = rightNodeHeight()
        val isRightNodeBalanced = rightNode?.isBalanced() ?: true
        return (leftNodeHeight - rightNodeHeight).absoluteValue <= 1
                && isLeftNodeBalanced
                && isRightNodeBalanced
    }

    override fun toString(): String {
        val stringBuilder = StringBuilder()
        traversePreOrder(stringBuilder, padding = "", pointer = "", node = this)
        return stringBuilder.toString()
    }

    private fun traversePreOrder(
        stringBuilder: StringBuilder,
        padding: String,
        pointer: String,
        node: BinaryTreeNode<Node, Value>?
    ) {
        if (node != null) {
            stringBuilder.append(padding)
            stringBuilder.append(pointer)
            stringBuilder.append(node.value)
            stringBuilder.append("\n")
            val paddingBuilder = StringBuilder(padding)
            paddingBuilder.append("│  ")
            val paddingForBoth = paddingBuilder.toString()
            val pointerForRight = "└──"
            val pointerForLeft = if (node.rightNode != null) "├──" else "└──"
            traversePreOrder(stringBuilder, paddingForBoth, pointerForLeft, node.leftNode)
            traversePreOrder(stringBuilder, paddingForBoth, pointerForRight, node.rightNode)
        }
    }

    private fun height(): Int {
        val leftNodeHeight = leftNodeHeight()
        val rightNodeHeight = rightNodeHeight()
        return 1 + max(leftNodeHeight, rightNodeHeight)
    }

    private fun leftNodeHeight(): Int {
        return leftNode?.height() ?: 0
    }

    private fun rightNodeHeight(): Int {
        return rightNode?.height() ?: 0
    }
}
