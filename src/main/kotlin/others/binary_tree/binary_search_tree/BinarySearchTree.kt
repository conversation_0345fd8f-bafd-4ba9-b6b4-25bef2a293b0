package others.binary_tree.binary_search_tree

import others.binary_tree.BinaryTreeNode

class BinarySearchTree {
    private var root: Node? = null

    companion object {
        fun create(sortedArray: List<Int>): BinarySearchTree {
            val newBST = BinarySearchTree()
            create(sortedArray, newBST)
            return newBST
        }

        private fun create(sortedArray: List<Int>, bst: BinarySearchTree) {
            if (sortedArray.isNotEmpty()) {
                val mid = sortedArray.size / 2
                val value = sortedArray[mid]
                bst.insert(value)
                create(sortedArray.subList(0, mid), bst)
                create(sortedArray.subList(mid + 1, sortedArray.size), bst)
            }
        }
    }

    fun insert(value: Int) {
        root?.insert(value) ?: run {
            root = Node(value)
        }
    }

    fun contains(value: Int): Boolean {
        return root?.contains(value) ?: false
    }

    fun printInorder() {
        root?.printInorder()
    }

    fun isBalanced(): Boolean {
        return root?.isBalanced() ?: true
    }

    fun toSortedArray(): List<Int> {
        return root?.let{
            val sortedArray = mutableListOf<Int>()
            convertToSortedArray(root, sortedArray)
            return sortedArray
        } ?: emptyList()
    }

    private fun convertToSortedArray(node: Node?, sortedArray: MutableList<Int>) {
        node?.let {
            convertToSortedArray(node.leftNode, sortedArray)
            sortedArray.add(node.value)
            convertToSortedArray(node.rightNode, sortedArray)
        }
    }

    override fun toString(): String {
        return root?.toString() ?: ""
    }

    inner class Node(
        override val value: Int,
        override var leftNode: Node? = null,
        override var rightNode: Node? = null
    ): BinaryTreeNode<Node, Int>(
        value, leftNode, rightNode
    ) {
        override fun insert(value: Int) {
            if (value < this.value) {
                leftNode?.insert(value) ?: run {
                    leftNode = Node(value)
                }
            } else {
                rightNode?.insert(value) ?: run {
                    rightNode = Node(value)
                }
            }
        }

        override fun contains(value: Int): Boolean {
            return if (this.value == value) {
                true
            } else {
                if (value < this.value) {
                    leftNode?.contains(value) ?: false
                } else {
                    rightNode?.contains(value) ?: false
                }
            }
        }

        override fun printInorder() {
            leftNode?.printInorder()
            print("$value ")
            rightNode?.printInorder()
        }
    }
}