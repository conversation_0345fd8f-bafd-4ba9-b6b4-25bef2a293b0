package others.binary_tree.binary_search_tree.test

import others.binary_tree.binary_search_tree.BinarySearchTree
import kotlin.test.assertFalse
import kotlin.test.assertTrue

fun main() {
    val binarySearchTree = BinarySearchTree()
    binarySearchTree.insert(50)
    binarySearchTree.insert(30)
    binarySearchTree.insert(20)
    binarySearchTree.insert(40)
    binarySearchTree.insert(70)
    binarySearchTree.insert(60)
    binarySearchTree.insert(80)

    println(binarySearchTree)

    assertTrue { binarySearchTree.contains(50) }
    assertTrue { binarySearchTree.contains(30) }
    assertTrue { binarySearchTree.contains(20) }
    assertTrue { binarySearchTree.contains(40) }
    assertTrue { binarySearchTree.contains(70) }
    assertTrue { binarySearchTree.contains(60) }
    assertTrue { binarySearchTree.contains(80) }

    assertFalse { binarySearchTree.contains(0) }
    assertFalse { binarySearchTree.contains(1) }
    assertFalse { binarySearchTree.contains(2) }

    // we should see: 20, 30, 40, 50, 60, 70, 80
    binarySearchTree.printInorder()
    println()

    println("the tree is a balanced tree?: ${binarySearchTree.isBalanced()}")

    repeat(60) {
        print("=")
    }
    println()
    println("Build an unbalanced tree")
    val unbalancedBinarySearchTree = BinarySearchTree()
    unbalancedBinarySearchTree.insert(4)
    unbalancedBinarySearchTree.insert(3)
    unbalancedBinarySearchTree.insert(2)
    unbalancedBinarySearchTree.insert(1)
    unbalancedBinarySearchTree.insert(5)
    println(unbalancedBinarySearchTree)
    println("the tree is a balanced tree?: ${unbalancedBinarySearchTree.isBalanced()}")

    repeat(60) {
        print("=")
    }
    println()
    println("convert an unbalanced tree into balanced tree")
    val sortedArray = unbalancedBinarySearchTree.toSortedArray()
    println(sortedArray)
    val newBST = BinarySearchTree.create(sortedArray)
    println(newBST)
    println("the tree is a balanced tree?: ${newBST.isBalanced()}")
}
