package others.find_permutations_in_string.test

import others.find_permutations_in_string.findPermutationsInString
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
}

private fun sample_1() {
    verify(
        longerString = "BACDGABCDA",
        shorterString = "ABCD",
        expected = listOf(0, 5, 6)
    )
}

private fun sample_2() {
    verify(
        longerString = "AAABABAA",
        shorterString = "AABA",
        expected = listOf(0, 1, 4)
    )
}

private fun verify(longerString: String, shorterString: String, expected: List<Int>) {
    val actual = findPermutationsInString(longerString, shorterString)
    assertEquals(expected.joinToString(), actual.joinToString())
}