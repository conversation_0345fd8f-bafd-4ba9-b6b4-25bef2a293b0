package others.find_permutations_in_string

/**
 * Given a text txt[0..n-1] and a pattern pat[0..m-1],
 * write a function search(char pat[], char txt[]) that prints all occurrences of pat[]
 * and its permutations (or anagrams) in txt[]. You may assume that n > m.
 * Expected time complexity is O(n)
 */
fun findPermutationsInString(longerString: String, shorterString: String): List<Int> {
    val sizeOfLongerString = longerString.length
    val sizeOfShorterString = shorterString.length

    val max = 256 // there are 256 ASCII

    val counterForLongerString = IntArray(max) { 0 }
    val counterForShorterString = IntArray(max) { 0 }

    val result = mutableListOf<Int>()
    for (index in 0..sizeOfLongerString) {
        if (index < sizeOfShorterString) {
            // initialize the counter
            counterForLongerString[longerString[index].toInt()]++
            counterForShorterString[shorterString[index].toInt()]++
        } else {
            // start the comparison
            val indexForComparison = index - sizeOfShorterString
            // this comparison is in constant speed => the counterForShorterString
            if (counterForShorterString contentEquals counterForLongerString) {
                result.add(indexForComparison)
            }

            // do not need to manage the counterForLongerString at the end of the process
            if (index < sizeOfLongerString) {
                counterForLongerString[longerString[index].toInt()]++
                counterForLongerString[longerString[indexForComparison].toInt()]--
            }
        }
    }

    return result
}