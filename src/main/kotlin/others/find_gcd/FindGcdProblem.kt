package others.find_gcd

import kotlin.math.absoluteValue
import kotlin.math.max
import kotlin.math.min

fun findGdc(input1: Int, input2: Int): Int {
    val largerValue = max(input1, input2).absoluteValue
    val smallerValue = min(input1, input2).absoluteValue

    var gcd = 0
    for (divisor in 1..smallerValue) {
        val largerValueCanDividedByDivisor = largerValue % divisor == 0
        val smallerValueCanDividedByDivisor = smallerValue % divisor == 0
        if (largerValueCanDividedByDivisor && smallerValueCanDividedByDivisor) {
            gcd = divisor
        }
    }
    return gcd
}