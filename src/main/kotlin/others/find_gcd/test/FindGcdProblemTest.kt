package others.find_gcd.test

import others.find_gcd.findGdc
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
    sample_9()
}

private fun sample_1() {
    verify(
        input1 = 24,
        input2 = 18,
        expectedResult = 6
    )
}

private fun sample_2() {
    verify(
        input1 = 12,
        input2 = 13,
        expectedResult = 1
    )
}

private fun sample_3() {
    verify(
        input1 = 18,
        input2 = 24,
        expectedResult = 6
    )
}

private fun sample_4() {
    verify(
        input1 = 0,
        input2 = 10,
        expectedResult = 0
    )
}

private fun sample_5() {
    verify(
        input1 = 0,
        input2 = 0,
        expectedResult = 0
    )
}

private fun sample_6() {
    verify(
        input1 = 25,
        input2 = 15,
        expectedResult = 5
    )
}

private fun sample_7() {
    verify(
        input1 = -25,
        input2 = 15,
        expectedResult = 5
    )
}

private fun sample_8() {
    verify(
        input1 = 25,
        input2 = -15,
        expectedResult = 5
    )
}

private fun sample_9() {
    verify(
        input1 = -25,
        input2 = -15,
        expectedResult = 5
    )
}

private fun verify(input1: Int, input2: Int, expectedResult: Int) {
    val actual = findGdc(input1, input2)
    assertEquals(expectedResult, actual)
}