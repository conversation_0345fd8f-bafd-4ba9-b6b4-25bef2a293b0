package others.find_value_in_pascal_triangle.test

import others.find_value_in_pascal_triangle.findValueInPascalTriangle
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
    sample_9()
    sample_10()
}

private fun sample_1() {
    val actual = findValueInPascalTriangle(1, 1)
    val expected = 1
    assertEquals(expected, actual)
}

private fun sample_2() {
    val actual = findValueInPascalTriangle(2, 1)
    val expected = 1
    assertEquals(expected, actual)
}

private fun sample_3() {
    val actual = findValueInPascalTriangle(2, 2)
    val expected = 1
    assertEquals(expected, actual)
}

private fun sample_4() {
    val actual = findValueInPascalTriangle(3, 1)
    val expected = 1
    assertEquals(expected, actual)
}

private fun sample_5() {
    val actual = findValueInPascalTriangle(3, 2)
    val expected = 2
    assertEquals(expected, actual)
}

private fun sample_6() {
    val actual = findValueInPascalTriangle(4, 1)
    val expected = 1
    assertEquals(expected, actual)
}

private fun sample_7() {
    val actual = findValueInPascalTriangle(4, 2)
    val expected = 3
    assertEquals(expected, actual)
}

private fun sample_8() {
    val actual = findValueInPascalTriangle(4, 3)
    val expected = 3
    assertEquals(expected, actual)
}

private fun sample_9() {
    val actual = findValueInPascalTriangle(5, 3)
    val expected = 6
    assertEquals(expected, actual)
}

private fun sample_10() {
    val actual = findValueInPascalTriangle(0, 0)
    val expected = 0
    assertEquals(expected, actual)
}