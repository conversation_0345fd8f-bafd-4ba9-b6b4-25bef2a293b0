package others.find_value_in_pascal_triangle

/*
  return a number within <PERSON>'s triangle at a certain X,Y coordinate
 */
fun findValueInPascalTriangle(row: Int, column: Int): Int {
    if (row == 0 || column == 0 || column > row) return 0
    return if (column == 1 || column == row) {
        1
    } else {
        val upperRow = row - 1
        findValueInPascalTriangle(upperRow, column - 1) + findValueInPascalTriangle(upperRow, column)
    }
}