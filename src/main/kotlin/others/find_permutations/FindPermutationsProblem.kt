package others.find_permutations

fun findPermutations(input: String): Array<String> {
    val allPermutations: Array<Array<String>> = Array(input.length) { arrayOf<String>() }
    for (index in input.indices) {
        val character = input[index].toString()
        if (index == 0) {
            allPermutations[index] = arrayOf(character)
        } else {
            val previousPermutations = allPermutations[index - 1]
            for (previousPermutation in previousPermutations) {
                for (i in 0..previousPermutation.length) {
                    allPermutations[index] = allPermutations[index] + StringBuilder(previousPermutation).insert(i, character).toString()
                }
            }
        }
    }
    return allPermutations.last()
}
