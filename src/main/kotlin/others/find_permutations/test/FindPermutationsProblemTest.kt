package others.find_permutations.test

import others.assertArrays
import others.find_permutations.findPermutations

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
}

private fun sample_1() {
    verify(
        input = "a",
        expected = arrayOf("a")
    )
}

private fun sample_2() {
    verify(
        input = "ab",
        expected = arrayOf("ab", "ba")
    )
}

private fun sample_3() {
    verify(
        input = "abc",
        expected = arrayOf("cab", "acb", "abc", "cba", "bca", "bac")
    )
}

private fun sample_4() {
    verify(
        input = "abb",
        expected = arrayOf("abb", "abb", "bab", "bba", "bab", "bba")
    )
}


private fun verify(input: String, expected: Array<String>) {
    val actual = findPermutations(input)
    assertArrays(expected, actual, ignoreOrder = true)
}
