package others.check_is_prime.test

import others.check_is_prime.isPrime
import kotlin.test.assertEquals

fun main() {
    for (number in 0..100) {
        val isPrime = isPrime(number)
        val expectedResult = when (number) {
            2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97 -> true
            else -> false
        }
        assertEquals(
            expectedResult,
            isPrime,
            message = "$number should ${if (!expectedResult) "NOT " else ""}be a prime number"
        )
    }
}

