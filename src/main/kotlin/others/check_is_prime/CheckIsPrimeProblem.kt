package others.check_is_prime

import kotlin.math.ceil
import kotlin.math.sqrt

fun isPrime(number: Int): Boolean {
    return when {
        number <= 1 -> {
            false
        }
        number == 2 -> {
            true
        }
        else -> {
            val loopLimit: Int = ceil(sqrt(number.toDouble())).toInt()
            for (i in 2..loopLimit) {
                if (number % i == 0) {
                    return false
                }
            }
            return true
        }
    }
}