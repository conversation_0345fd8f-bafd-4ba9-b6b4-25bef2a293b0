package others.stack

interface MyStack {
    fun push(v: Int)
    fun pop(): Int
    fun inc(i: Int, v: Int)
    fun empty(): Boolean
    fun peek(): Int
    fun sum(): Long
}

/**
 * assume that the peek and pop would not be called in empty state
 */
class MyStackImpl : MyStack {
    private val size = 100000
    private val array = Array<Int?>(size) { null }
    private var headIndex = -1

    override fun push(v: Int) {
        array[++headIndex] = v
    }

    override fun pop(): Int {
        val v = array[headIndex]
        array[headIndex] = null
        headIndex--
        return v!!
    }

    override fun inc(i: Int, v: Int) {
        for (index in 0 until i) {
            if (array[index] != null) {
                array[index] = array[index]!! + v
            }
        }
    }

    override fun empty(): Boolean {
        return headIndex == -1
    }

    override fun peek(): Int {
        return array[headIndex]!!
    }

    override fun sum(): Long {
        var sum = 0L
        var index = 0
        while (array[index] != null) {
            sum += array[index++]!!
        }
        return sum
    }
}