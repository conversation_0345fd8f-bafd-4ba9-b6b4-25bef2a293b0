package others.stack

import kotlin.test.assertEquals

fun main() {
    val stack: MyStack = MyStackImpl()

    assertEquals(0L, stack.sum())
    assertEquals(true, stack.empty())

    stack.push(4)
    assertEquals(4L, stack.sum())
    assertEquals(4, stack.peek())
    assertEquals(false, stack.empty())

    stack.push(5)
    assertEquals(9L, stack.sum())
    assertEquals(5, stack.peek())

    stack.inc(2, 1)
    assertEquals(11L, stack.sum())
    assertEquals(6, stack.peek())

    assertEquals(6, stack.pop())
    assertEquals(5, stack.peek())
    assertEquals(5L, stack.sum())
    assertEquals(false, stack.empty())

    assertEquals(5, stack.pop())
    assertEquals(0L, stack.sum())
    assertEquals(true, stack.empty())
}