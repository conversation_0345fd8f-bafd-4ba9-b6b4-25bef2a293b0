package others.stack

import java.util.*

// https://leetcode.com/problems/number-of-atoms/?envType=problem-list-v2&envId=m4u9eqwr
fun main() {
     println(countOfAtoms("H2O")) // H2)
     println(countOfAtoms("Mg(OH)2")) // H2MgO2
     println(countOfAtoms("(Mg(OH)2)3")) // H6Mg3O6
     println(countOfAtoms("K4(ON(SO3)2)2")) // K4N2O14S4
     println(countOfAtoms("A1")) // A
     println(countOfAtoms("(A(BC)2(DE)3)4")) // A4B8C8D12E12
     println(countOfAtoms("(A(AB)2(AC)3)4")) // A24B8C12
     println(countOfAtoms("(NB3)33")) // B99N33
}

private fun countOfAtoms(formula: String): String {
    // time: O(n^2)
    // space: O(n)
    var index = 0
    val stack = Stack<TreeMap<String, Int>>()
    stack.push(TreeMap<String, Int>())
    while (index <= formula.lastIndex) {
        when (formula[index]) {
            '(' -> {
                stack.push(TreeMap<String, Int>())
                index++
            }
            ')' -> {
                index++
                var count = 0
                while (index <= formula.lastIndex && formula[index].isDigit()) {
                    count = count * 10 + formula[index++].digitToInt()
                }
                if (count == 0) count = 1
                val currentMap = stack.pop()
                for ((key, value) in currentMap) {
                    currentMap[key] = value * count
                }
                val previousMap = stack.peek()
                for ((key, value) in currentMap) {
                    previousMap[key] = previousMap.getOrDefault(key, 0) + value
                }
            }
            else -> {
                val sb = StringBuilder()
                while (
                    index <= formula.lastIndex && formula[index] != '(' && formula[index] != ')'
                    && !formula[index].isDigit() && (sb.isEmpty() || formula[index].isLowerCase())
                ) {
                    sb.append(formula[index++])
                }
                var count = 0
                while (index <= formula.lastIndex && formula[index].isDigit()) {
                    count = count * 10 + formula[index++].digitToInt()
                }
                val key = sb.toString()
                if (count == 0) count = 1
                stack.peek()[key] = stack.peek().getOrDefault(key, 0) + count
            }
        }
    }
    return parse(stack.pop())
}

private fun parse(map: Map<String, Int>): String {
    val sb = StringBuilder()
    for ((key, value) in map) {
        sb.append(key)
        if (value > 1) sb.append(value)
    }
    return sb.toString()
}
