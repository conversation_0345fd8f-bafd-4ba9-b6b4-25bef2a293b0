package others.stack

import java.util.*

// https://leetcode.com/problems/basic-calculator/
fun main() {
    println(calculate("1 + 1"))
    println(calculate(" 2-1 + 2 "))
    println(calculate("-(1+2)"))
    println(calculate("-    (1+2)"))
    println(calculate("(1+(4+5+2)-3)+(6+8)"))
    println(calculate("1-(     -2)"))
}

private     fun calculate(s: String): Int {
    // time: O(n)
    // space: O(n)
    val signStack = Stack<Int>()
    var num = 0
    var sign = 1
    var result = 0
    signStack.push(sign)
    for (c in s) {
        if (c.isDigit()) {
            num = 10 * num + (c - '0')
        } else if (c == '+' || c == '-') {
            result += sign * num
            num = 0
            sign = signStack.peek() * if (c == '+') 1 else -1
        } else if (c == '(') {
            signStack.push(sign)
        } else if (c == ')') {
            signStack.pop()
        }
    }
    result += sign * num
    return result
}
