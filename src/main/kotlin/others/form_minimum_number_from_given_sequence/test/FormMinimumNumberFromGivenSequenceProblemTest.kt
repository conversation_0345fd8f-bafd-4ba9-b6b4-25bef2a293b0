package others.form_minimum_number_from_given_sequence.test

import others.assertArrays
import others.form_minimum_number_from_given_sequence.formMinimumNumberFromGivenSequence

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
}

private fun sample_0() {
    verify(
        input = "",
        expected = intArrayOf()
    )
}

private fun sample_1() {
    verify(
        input = "D",
        expected = intArrayOf(2, 1)
    )
}

private fun sample_2() {
    verify(
        input = "I",
        expected = intArrayOf(1, 2)
    )
}

private fun sample_3() {
    verify(
        input = "DD",
        expected = intArrayOf(3, 2, 1)
    )
}

private fun sample_4() {
    verify(
        input = "II",
        expected = intArrayOf(1, 2, 3)
    )
}

private fun sample_5() {
    verify(
        input = "DIDI",
        expected = intArrayOf(2, 1, 4, 3, 5)
    )
}

private fun sample_6() {
    verify(
        input = "IIDDD",
        expected = intArrayOf(1, 2, 6, 5, 4, 3)
    )
}

private fun sample_7() {
    verify(
        input = "DDIDDIID",
        expected = intArrayOf(3, 2, 1, 6, 5, 4, 7, 9, 8)
    )
}

private fun verify(input: String, expected: IntArray) {
    val actual = formMinimumNumberFromGivenSequence(input)
    assertArrays(expected.toTypedArray(), actual.toTypedArray())
}