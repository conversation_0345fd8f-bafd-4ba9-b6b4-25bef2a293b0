package others.form_minimum_number_from_given_sequence

import java.util.*

/**
 * Given a pattern containing only I’s and D’s.
 * I for increasing and D for decreasing.
 * Devise an algorithm to print the minimum number following that pattern.
 * Digits from 1-9 and digits can’t repeat.
 */
fun formMinimumNumberFromGivenSequence(input: String): IntArray {
    return if (input.isBlank()) {
        intArrayOf()
    } else {
        val stack = Stack<Int>()
        var result = intArrayOf()
        for (commandIndex in input.indices) {
            val command = input[commandIndex]
            val value = commandIndex + 1
            stack.push(value)
            if (command == 'I') {
                while (stack.isNotEmpty()) {
                    result += stack.pop()
                }
            }
        }
        // handle the last value
        stack.push(input.length + 1)

        while (stack.isNotEmpty()) {
            result += stack.pop()
        }
        result
    }
}