package others.dijkstra

/**
 * https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/
 *
 * This one is going to show the Djikstra's algorithm able to handle un-directed graph
 */

fun findShortestDistances(graph: Array<IntArray>, sourceNodeIndex: Int): IntArray {
    // define a shortestDistances array to stored the shortest distance from source to node i
    val shortestDistancesFromSource = IntArray(graph.size) { Int.MAX_VALUE }
    shortestDistancesFromSource[sourceNodeIndex] = 0

    val visitedNodes = BooleanArray(graph.size) { false }

    repeat(visitedNodes.size) {
        val nodeIndexWithMinShortestDistanceFromSource =
            findNodeIndexWithMinShortestDistanceFromSource(shortestDistancesFromSource, visitedNodes)
        if (nodeIndexWithMinShortestDistanceFromSource == -1) {
            // the rest of the nodes are not connected to source
            return shortestDistancesFromSource
        }
        visitedNodes[nodeIndexWithMinShortestDistanceFromSource] = true

        // update the shortest distance from nodeIndexWithMinShortestDistanceFromSource to its adjacency
        val distanceFromSourceToCurrentNode = shortestDistancesFromSource[nodeIndexWithMinShortestDistanceFromSource]
        for (adjacencyIndex in graph.indices) {
            // alt := dist[v] + length(v, u)
            val distanceFromCurrentNodeToAdjacency = graph[nodeIndexWithMinShortestDistanceFromSource][adjacencyIndex]
            val isConnected = distanceFromCurrentNodeToAdjacency != 0
            val notVisitedYet = !visitedNodes[adjacencyIndex]
            if (isConnected && notVisitedYet) {
                val distanceFromSourceToAdjacency = distanceFromSourceToCurrentNode + distanceFromCurrentNodeToAdjacency
                if (distanceFromSourceToAdjacency < shortestDistancesFromSource[adjacencyIndex]) {
                    shortestDistancesFromSource[adjacencyIndex] = distanceFromSourceToAdjacency
                }
            }
        }
    }

    return shortestDistancesFromSource
}

fun findNodeIndexWithMinShortestDistanceFromSource(
    shortestDistancesFromSource: IntArray,
    visitedNodes: BooleanArray
): Int {
    var shortestDistance = Int.MAX_VALUE
    var nodeIndexWithShortestDistance = -1
    shortestDistancesFromSource.forEachIndexed { index, distanceFromSource ->
        val notVisitedYet = !visitedNodes[index]
        if (notVisitedYet && distanceFromSource < shortestDistance) {
            shortestDistance = distanceFromSource
            nodeIndexWithShortestDistance = index
        }
    }
    return nodeIndexWithShortestDistance
}

fun showShortestDistances(shortestDistances: IntArray, sourceNodeIndex: Int) {
    println("Vertex \t\t Distance from Source: $sourceNodeIndex")
    shortestDistances.forEachIndexed { index, shortestDistance ->
        println("$index \t\t $shortestDistance")
    }
}

fun main() {
    /* Let us create the example graph discussed above */
    val graph = arrayOf(
        intArrayOf(0, 4, 0, 0, 0, 0, 0, 8, 0, 0),
        intArrayOf(4, 0, 8, 0, 0, 0, 0, 11, 0, 0),
        intArrayOf(0, 8, 0, 7, 0, 4, 0, 0, 2, 0),
        intArrayOf(0, 0, 7, 0, 9, 14, 0, 0, 0, 0),
        intArrayOf(0, 0, 0, 9, 0, 10, 0, 0, 0, 0),
        intArrayOf(0, 0, 4, 14, 10, 0, 2, 0, 0, 0),
        intArrayOf(0, 0, 0, 0, 0, 2, 0, 1, 6, 0),
        intArrayOf(8, 11, 0, 0, 0, 0, 1, 0, 7, 0),
        intArrayOf(0, 0, 2, 0, 0, 0, 6, 7, 0, 0),
        intArrayOf(0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
    )
    val sourceNodeIndex = 0
    val shortestDistances = findShortestDistances(graph, sourceNodeIndex)
    showShortestDistances(shortestDistances, sourceNodeIndex)
}

