package others.dijkstra

import others.assertArrays

/**
 * https://brilliant.org/wiki/dijkstras-short-path-finder/
 */

fun main() {
    val s = Vertex(id = "S", index = 0)
    val a = Vertex(id = "A", index = 1)
    val b = Vertex(id = "B", index = 2)
    val c = Vertex(id = "C", index = 3)
    val d = Vertex(id = "D", index = 4)
    val e = Vertex(id = "E", index = 5)
    val f = Vertex(id = "F", index = 6)

    s.addEdge(Edge(distance = 3, destination = a))
    s.addEdge(Edge(distance = 2, destination = c))
    s.addEdge(Edge(distance = 6, destination = f))

    a.addEdge(Edge(distance = 6, destination = b))
    a.addEdge(Edge(distance = 1, destination = d))

    b.addEdge(Edge(distance = 1, destination = e))

    c.addEdge(Edge(distance = 2, destination = a))
    c.addEdge(Edge(distance = 3, destination = d))

    d.addEdge(Edge(distance = 4, destination = e))

    f.addEdge(Edge(distance = 2, destination = e))

    val graph = listOf(s, a, b, c, d, e, f)
    val result = dijkstra(graph, s)
    assertArrays(result, arrayOf(0, 3, 9, 2, 4, 8, 6))

    println("========== shortest path from S to all nodes ============")
    graph.forEach { vertex ->
        println("(S) -> (${vertex.id}): ${result[vertex.index]}")
    }
}

private fun dijkstra(graph: List<Vertex>, source: Vertex): Array<Int> {
    /*
     * it is storing the distance from source to node
     * distance[1] means the distance from source to node with index 1
     * where distance[0] is the source node
     */
    val distances = Array(graph.size) { Int.MAX_VALUE }
    // the distance from source to source must be 0
    distances[0] = 0

    val unvisitedNodes = mutableSetOf<Vertex>()
    graph.forEach { vertex ->
        unvisitedNodes.add(vertex)
    }

    while (unvisitedNodes.isNotEmpty()) {
        val minAndUnvisitedVertex = findVertexWithMinDistance(unvisitedNodes, distances)
        unvisitedNodes.remove(minAndUnvisitedVertex)
        println("visiting node: " + minAndUnvisitedVertex.id)

        minAndUnvisitedVertex.outputGoingEdges.forEach { edge ->
            val minDistanceFromSourceToNeighbor = distances[minAndUnvisitedVertex.index] + edge.distance
            if (minDistanceFromSourceToNeighbor < distances[edge.destination.index]) {
                distances[edge.destination.index] = minDistanceFromSourceToNeighbor
            }
        }
    }
    return distances
}

private fun findVertexWithMinDistance(unvisitedNodes: Set<Vertex>, distances: Array<Int>): Vertex {
    var minVertex: Vertex? = null
    var minDistance = Integer.MAX_VALUE
    for (vertex in unvisitedNodes) {
        val index = vertex.index
        val distance = distances[index]
        if (distance < minDistance) {
            minDistance = distance
            minVertex = vertex
        }
    }
    return minVertex!!
}


private data class Vertex(
    val id: String,
    val index: Int,
    val outputGoingEdges: MutableSet<Edge> = mutableSetOf()
) {
    fun addEdge(edge: Edge) {
        outputGoingEdges.add(edge)
    }
}

private data class Edge(
    val distance: Int,
    val destination: Vertex
)
