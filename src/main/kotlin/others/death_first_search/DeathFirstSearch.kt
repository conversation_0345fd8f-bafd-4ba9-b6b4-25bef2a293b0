package others.death_first_search

import others.Node
import java.util.*

fun main() {
    val root = Node(data = 1)
    root.left = Node(data = 2)
    root.right = Node(data = 3)
    root.left?.left = Node(data = 4)
    root.left?.right = Node(data = 5)
    root.right?.left = Node(data = 6)
    root.right?.right = Node(data = 7)

    println(deathFirstSearchUseStack(node = root, target = 1))
    println(deathFirstSearchUseStack(node = root, target = 2))
    println(deathFirstSearchUseStack(node = root, target = 3))
    println(deathFirstSearchUseStack(node = root, target = 4))
    println(deathFirstSearchUseStack(node = root, target = 5))
    println(deathFirstSearchUseStack(node = root, target = 6))
    println(deathFirstSearchUseStack(node = root, target = 7))
    println(deathFirstSearchUseStack(node = root, target = 8))

    println(deathFirstSearchUseRecursion(node = root, target = 1))
    println(deathFirstSearchUseRecursion(node = root, target = 2))
    println(deathFirstSearchUseRecursion(node = root, target = 3))
    println(deathFirstSearchUseRecursion(node = root, target = 4))
    println(deathFirstSearchUseRecursion(node = root, target = 5))
    println(deathFirstSearchUseRecursion(node = root, target = 6))
    println(deathFirstSearchUseRecursion(node = root, target = 7))
    println(deathFirstSearchUseRecursion(node = root, target = 8))
}

fun deathFirstSearchUseStack(node: Node<Int>?, target: Int): Node<Int>? {
    if (node == null) return null

    val stack = Stack<Node<Int>>()
    stack.add(node)
    while (stack.isNotEmpty()) {
        val n = stack.pop()
        print("${n.data}, ")
        if (n.data == target) return n

        n.right?.let { stack.add(it) }
        n.left?.let { stack.add(it) }
    }
    return null
}

fun deathFirstSearchUseRecursion(node: Node<Int>?, target: Int): Node<Int>? {
    if (node == null) return null
    print("${node.data}, ")

    if (node.data == target) return node

    val leftResult = deathFirstSearchUseRecursion(node.left, target)
    if (leftResult != null) return leftResult

    val rightResult = deathFirstSearchUseRecursion(node.right, target)
    if (rightResult != null) return rightResult

    return null
}
