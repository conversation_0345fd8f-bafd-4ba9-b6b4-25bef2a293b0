package others.implement_deque.test

import others.implement_deque.MyDeque
import java.lang.IllegalStateException
import java.util.NoSuchElementException
import kotlin.test.assertEquals

fun main() {
    val deque = MyDeque<String>()
    assertEquals(0, deque.size)
    assertEquals(null, deque.peekFirst())
    assertEquals(null, deque.peekLast())

    try {
        deque.removeFirst()
        throw IllegalStateException("we should not reach here")
    } catch (e: NoSuchElementException) {
        // do nothing
    }

    try {
        deque.removeLast()
        throw IllegalStateException("we should not reach here")
    } catch (e: NoSuchElementException) {
        // do nothing
    }

    deque.addFirst("1")
    assertEquals(1, deque.size)
    assertEquals("1", deque.peekFirst())
    assertEquals("1", deque.peekLast())
    assertEquals(1, deque.size)

    deque.addFirst("2")
    assertEquals(2, deque.size)
    assertEquals("2", deque.peekFirst())
    assertEquals("1", deque.peekLast())

    deque.addLast("3")
    assertEquals(3, deque.size)
    assertEquals("2", deque.peekFirst())
    assertEquals("3", deque.peekLast())

    assertEquals("2", deque.removeFirst())
    assertEquals(2, deque.size)
    assertEquals("1", deque.peekFirst())
    assertEquals("3", deque.peekLast())

    assertEquals("3", deque.removeLast())
    assertEquals(1, deque.size)
    assertEquals("1", deque.peekFirst())
    assertEquals("1", deque.peekLast())

    assertEquals("1", deque.removeLast())
    assertEquals(0, deque.size)
    assertEquals(null, deque.peekFirst())
    assertEquals(null, deque.peekLast())

    try {
        deque.removeFirst()
        throw IllegalStateException("we should not reach here")
    } catch (e: NoSuchElementException) {
        // do nothing
    }

    try {
        deque.removeLast()
        throw IllegalStateException("we should not reach here")
    } catch (e: NoSuchElementException) {
        // do nothing
    }
}
