package others.implement_deque

import java.util.*
import kotlin.NoSuchElementException

class MyDeque<T>: Deque<T?> {
    @Suppress("UNCHECKED_CAST")
    private var elements = arrayOf<Any?>() as Array<T?>

    override fun addFirst(e: T?) {
        @Suppress("UNCHECKED_CAST")
        elements = arrayOf<Any?>(e) as Array<T?> + elements
    }

    override fun addLast(e: T?) {
        elements += e
    }

    override fun removeFirst(): T? {
        if (elements.isNotEmpty()) {
            val firstValue = elements.first()
            elements = elements.copyOfRange(1, elements.size)
            return firstValue
        } else {
            throw NoSuchElementException("the queue is empty")
        }
    }

    override fun removeLast(): T? {
        if (elements.isNotEmpty()) {
            val lastValue = elements.last()
            elements = elements.copyOfRange(0, elements.size - 1)
            return lastValue
        } else {
            throw NoSuchElementException("the queue is empty")
        }
    }

    override fun peekFirst(): T? {
        return if (elements.isNotEmpty()) {
            elements.first()
        } else {
            null
        }
    }

    override fun peekLast(): T? {
        return if (elements.isNotEmpty()) {
            elements.last()
        } else {
            null
        }
    }

    override val size: Int
        get() = elements.size

    // we do not need to implement these

    override fun contains(element: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun addAll(elements: Collection<T?>): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun clear() {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun element(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun push(e: T?) {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun getLast(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun removeAll(elements: Collection<T?>): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun add(element: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun offer(e: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun iterator(): MutableIterator<T?> {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun peek(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun offerLast(e: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun getFirst(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun removeLastOccurrence(o: Any?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun isEmpty(): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun remove(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun remove(element: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun containsAll(elements: Collection<T?>): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun offerFirst(e: T?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun pollFirst(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun retainAll(elements: Collection<T?>): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun pollLast(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun pop(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun removeFirstOccurrence(o: Any?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun poll(): T? {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun descendingIterator(): MutableIterator<T?> {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }
}