package others.queue.test

import others.queue.MakeQueueProblem
import java.lang.IllegalStateException

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
}

private fun sample_1() {
    val queue = MakeQueueProblem.MyQueue()
    assert(null, queue.deQueue())
}

private fun sample_2() {
    val queue = MakeQueueProblem.MyQueue()
    queue.enQueue("1")
    assert("1", queue.deQueue())
}

private fun sample_3() {
    val queue = MakeQueueProblem.MyQueue()
    queue.enQueue("1")
    queue.enQueue("2")
    assert("1", queue.deQueue())
}

private fun sample_4() {
    val queue = MakeQueueProblem.MyQueue()
    queue.enQueue("1")
    queue.enQueue("2")
    assert("1", queue.deQueue())
    assert("2", queue.deQueue())
    assert(null, queue.deQueue())
}

private fun assert(expected: String?, actual: String?) {
    if (actual != expected) {
        throw IllegalStateException("\"$actual\" should be equal to \"$expected\"")
    }
}

