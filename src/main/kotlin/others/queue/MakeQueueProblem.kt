package others.queue

import java.util.*

/**
 * implement a fifo queue wif 2 filo stack
 */
class MakeQueueProblem {
    class MyQueue {
        private var normalStack: Stack<String> = Stack()
        private var reverseStack: Stack<String> = Stack()

        fun enQueue(s: String) {
            normalStack.push(s)
        }

        fun deQueue(): String? {
            return if (normalStack.empty()) {
                null
            } else {
                while (!normalStack.empty()) {
                    reverseStack.push(normalStack.pop())
                }
                val result = reverseStack.pop()
                while (!reverseStack.empty()) {
                    normalStack.push(reverseStack.pop())
                }
                return result
            }
        }
    }
}