package others.breadth_first_search

import others.Node
import java.util.LinkedList

fun main() {
    val root = Node(data = 1)
    root.left = Node(data = 2)
    root.right = Node(data = 3)
    root.left?.left = Node(data = 4)
    root.left?.right = Node(data = 5)
    root.right?.left = Node(data = 6)
    root.right?.right = Node(data = 7)

    println(breadthFirstSearch(node = root, target = 1))
    println(breadthFirstSearch(node = root, target = 2))
    println(breadthFirstSearch(node = root, target = 3))
    println(breadthFirstSearch(node = root, target = 4))
    println(breadthFirstSearch(node = root, target = 5))
    println(breadthFirstSearch(node = root, target = 6))
    println(breadthFirstSearch(node = root, target = 7))
    println(breadthFirstSearch(node = root, target = 8))
}

fun breadthFirstSearch(node: Node<Int>?, target: Int): Node<Int>? {
    if (node == null) return null

    val queue = LinkedList<Node<Int>>()
    queue.add(node)
    while (queue.isNotEmpty()) {
        val n = queue.pop()
        print("${n.data}, ")
        if (n.data == target) return n

        n.left?.let { queue.add(it) }
        n.right?.let { queue.add(it) }
    }
    return null
}