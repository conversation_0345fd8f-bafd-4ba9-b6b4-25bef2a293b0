package others.largest_connected_component_on_a_grid

internal object FindLargestConnectedComponentOnAGridProblem {
    private const val numberOfRows = 6
    private const val numberOfColumns = 8

    // stores information about which cell are already visited in a particular BFS
    private val visited = Array(numberOfRows) { IntArray(numberOfColumns) }

    // result stores the final result grid
    private val result = Array(numberOfRows) { IntArray(numberOfColumns) }

    // stores the count of cells in the largest connected component
    private var COUNT = 0

    // Function checks if a cell is valid i.e it is inside the grid and equal to the key
    private fun isValid(
        rowIndex: Int, columnIndex: Int,
        key: Int,
        input: Array<IntArray>
    ): Boolean {
        val pointIsInGrid = rowIndex < numberOfRows && columnIndex < numberOfColumns && rowIndex >= 0 && columnIndex >= 0
        return if (pointIsInGrid) {
            val notVisitedYet = visited[rowIndex][columnIndex] == 0
            val isSameValueAsKey = input[rowIndex][columnIndex] == key
            notVisitedYet && isSameValueAsKey
        } else {
            false
        }
    }

    // BFS to find all cells in connection with key = input[i][j]
    private fun breadthFirstSearch(
        currentKey: Int, rightNodeOrDownwardNodeKey: Int, rowIndex: Int, columnIndex: Int, input: Array<IntArray>
    ) {
        // terminating case for BFS
        if (currentKey != rightNodeOrDownwardNodeKey) {
            return
        }
        visited[rowIndex][columnIndex] = 1
        COUNT++

        // below arrays are the possible movements in row or column direction
        val columnMove = intArrayOf(0, 0, 1, -1)
        val rowMove = intArrayOf(1, -1, 0, 0)

        // checks all four points connected with input[i][j]
        for (u in 0..3) {
            val newRowIndex = rowIndex + rowMove[u]
            val newColumnIndex = columnIndex + columnMove[u]
            if (isValid(
                    newRowIndex,
                    newColumnIndex,
                    currentKey, input
                )
            ) {
                breadthFirstSearch(
                    currentKey, rightNodeOrDownwardNodeKey, newRowIndex,
                    newColumnIndex, input
                )
            }
        }
    }

    // called every time before a BFS so that visited array is reset to zero
    private fun resetVisited() {
        for (i in 0 until numberOfRows) {
            for (j in 0 until numberOfColumns) {
                visited[i][j] = 0
            }
        }
    }

    // If a larger connected component is found this function is called to store information about that component.
    private fun resetResult(
        key: Int,
        input: Array<IntArray>
    ) {
        for (i in 0 until numberOfRows) {
            for (j in 0 until numberOfColumns) {
                val isVisited = visited[i][j] == 1
                val sameValueAsKey = input[i][j] == key
                if (isVisited && sameValueAsKey) {
                    result[i][j] = key
                } else {
                    result[i][j] = 0
                }
            }
        }
    }

    // function to print the result
    private fun printResult(res: Int) {
        println("The largest connected component of the grid is : $res")

        // prints the largest component
        for (i in 0 until numberOfRows) {
            for (j in 0 until numberOfColumns) {
                if (result[i][j] != 0) {
                    print("${result[i][j]} ")
                } else {
                    print(". ")
                }
            }
            println()
        }
    }

    // function to calculate the largest connected component
    fun computeLargestConnectedGrid(input: Array<IntArray>) {
        var currentMax = Int.MIN_VALUE
        for (rowIndex in 0 until numberOfRows) {
            for (columnIndex in 0 until numberOfColumns) {
                resetVisited()
                COUNT = 0

                // checking cell to the right
                val currentKey = input[rowIndex][columnIndex]
                if (columnIndex + 1 < numberOfColumns) {
                    breadthFirstSearch(
                        currentKey, input[rowIndex][columnIndex + 1],
                        rowIndex, columnIndex, input
                    )
                }

                // updating result
                if (COUNT >= currentMax) {
                    currentMax = COUNT
                    resetResult(currentKey, input)
                }
                resetVisited()
                COUNT = 0

                // checking cell downwards
                if (rowIndex + 1 < numberOfRows) {
                    breadthFirstSearch(
                        currentKey,
                        input[rowIndex + 1][columnIndex], rowIndex, columnIndex, input
                    )
                }

                // updating result
                if (COUNT >= currentMax) {
                    currentMax = COUNT
                    resetResult(currentKey, input)
                }
            }
        }
        printResult(currentMax)
    }
}

fun main(args: Array<String>) {
    val input = arrayOf(
        intArrayOf(8, 4, 4, 4, 4, 3, 3, 8),
        intArrayOf(2, 8, 8, 4, 3, 3, 8, 8),
        intArrayOf(3, 2, 8, 8, 2, 3, 2, 8),
        intArrayOf(3, 3, 2, 8, 2, 2, 2, 2),
        intArrayOf(3, 8, 3, 8, 8, 4, 4, 4),
        intArrayOf(8, 8, 3, 8, 8, 4, 4, 4)
    )

    // function to compute the largest connected component in the grid
    FindLargestConnectedComponentOnAGridProblem.computeLargestConnectedGrid(input)
}
