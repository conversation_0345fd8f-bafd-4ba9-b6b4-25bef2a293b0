package others.fast_query_design.test

import others.fast_query_design.FastQueryDesign
import kotlin.test.assertEquals

fun main() {
    val data = arrayOf(
        intArrayOf(1, 2, 3, 4, 5),
        intArrayOf(11, 12, 13, 14, 15),
        intArrayOf(21, 22, 23, 24, 25),
        intArrayOf(31, 32, 33, 34, 35),
        intArrayOf(41, 42, 43, 44, 45),
    )
    val fastQueryDesign = FastQueryDesign(data)

    assertEquals(1, fastQueryDesign.query(Pair(0, 0), Pair(0, 0)))
    assertEquals(15, fastQueryDesign.query(Pair(0, 0), Pair(4, 0)))
    assertEquals(12, fastQueryDesign.query(Pair(2, 0), Pair(4, 0)))
    assertEquals(105, fastQueryDesign.query(Pair(0, 0), Pair(0, 4)))
    assertEquals(207, fastQueryDesign.query(Pair(1, 1), Pair(3, 3)))

    fastQueryDesign.update(Pair(0, 0), 101)
    assertEquals(101, fastQueryDesign.query(Pair(0, 0), Pair(0, 0)))
    assertEquals(115, fastQueryDesign.query(Pair(0, 0), Pair(4, 0)))
    assertEquals(205, fastQueryDesign.query(Pair(0, 0), Pair(0, 4)))
}