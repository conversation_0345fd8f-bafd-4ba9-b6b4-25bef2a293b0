package others.fast_query_design

/**
 * Given a 2D matrix M X N, support two operations:
 * Query(row1, col1, row2, col2) such that I get the sum of all numbers in the rectangle
 * ((row1, col1), (row1, col2), (row2, col1), (row2, col2)) and
 * Update(row, col) to a new number
 *
 * And query is a very frequent operation and update is a rare operation,
 * so query should be really fast, but update can be slower.
 *
 * Follow up: How would you solve this in a distributed fashion
 */

class FastQueryDesign(
    private val data: Array<IntArray>
) {
    fun query(upperLeftPoint: Pair<Int, Int>, lowerRightPoint: Pair<Int, Int>): Int {
        var sum = 0
        for (rowIndex in upperLeftPoint.second..lowerRightPoint.second) {
            for (columnIndex in upperLeftPoint.first..lowerRightPoint.first) {
                sum += data[rowIndex][columnIndex]
            }
        }
        return sum
    }

    fun update(point: Pair<Int, Int>, newValue: Int) {
        data[point.second][point.first] = newValue
    }
}
