package others.greatest_common_divisor

import kotlin.test.assertEquals

fun main() {
    assertEquals(0, findGreatestCommonDivisor(0, 0))
    assertEquals(1, findGreatestCommonDivisor(1, 0))
    assertEquals(1, findGreatestCommonDivisor(0, 1))
    assertEquals(6, findGreatestCommonDivisor(54, 24))
    assertEquals(6, findGreatestCommonDivisor(24, 54))
    assertEquals(1, findGreatestCommonDivisor(3, 7))

    // use dynamic approach to calculate the gcd
    // https://en.wikipedia.org/wiki/Greatest_common_divisor
    val max = 100
    val gcd = Array(max) { IntArray(max) { 0 } }
    for (a in 0 until max) {
        for (b in 0 until max) {
            when {
                a == 0 -> {
                    gcd[a][b] = b
                }
                b == 0 -> {
                    gcd[a][b] = a
                }
                a > b -> {
                    gcd[a][b] = gcd[a - b][b]
                }
                a == b -> {
                    gcd[a][b] = a
                }
                else -> {
                    gcd[a][b] = gcd[a][b - a]
                }
            }
        }
    }

    assertEquals(0, gcd[0][0])
    assertEquals(1, gcd[1][0])
    assertEquals(1, gcd[0][1])
    assertEquals(6, gcd[54][24])
    assertEquals(6, gcd[24][54])
    assertEquals(1, gcd[3][7])
}

/**
 * https://en.wikipedia.org/wiki/Euclidean_algorithm
 */
fun findGreatestCommonDivisor(a: Int, b: Int): Int {
    if (a == 0) return b
    if (b == 0) return a
    return findGreatestCommonDivisor(b, a % b)
}

