package others.cut_off_trees_for_golf_event

import java.util.*
import kotlin.test.assertEquals

/**
 * https://leetcode.com/problems/cut-off-trees-for-golf-event/
 */

fun cutOffTree(forest: List<List<Int>>): Int {
    // because we would go from shortest tree to tallest tree. min heap is the perfect data structure
    val minHeap = PriorityQueue<Node> { n1, n2 -> n1.treeHeight - n2.treeHeight }
    for (rowIndex in forest.indices) {
        val row = forest[rowIndex]
        for (columnIndex in row.indices) {
            val treeHeight = row[columnIndex]
            if (treeHeight > 1) {
                /*
                 * 0 means the cell cannot be walked through
                 * 1 means empty cell (no tree)
                 *
                 * So we skip both cases
                 */
                val node = Node(
                    rowIndex = rowIndex,
                    columnIndex = columnIndex,
                    treeHeight = treeHeight
                )
                minHeap.add(node)
            }
        }
    }

    // the starting point must be (0, 0)
    var currentNode = Node(
        rowIndex = 0,
        columnIndex = 0,
        treeHeight = forest[0][0]
    )
    var answer = 0
    while (minHeap.isNotEmpty()) {
        val nextNode = minHeap.poll()
        val shortestDistance = findShortestDistance(forest, currentNode, nextNode)
        if (shortestDistance == -1) {
            return -1
        } else {
            answer += shortestDistance
            currentNode = nextNode
        }
    }
    return answer
}

/**
 * Use BFS to find the shortest distance from the current node to the next node
 * If the next node is not reachable, return -1
 */
fun findShortestDistance(forest: List<List<Int>>, currentNode: Node, nextNode: Node): Int {
    val visited = Array(forest.size) { BooleanArray(forest[0].size) { false } }
    val cellQueue: Queue<Cell> = LinkedList()

    visited[currentNode.rowIndex][currentNode.columnIndex] = true
    cellQueue.add(
        Cell(
            rowIndex = currentNode.rowIndex,
            columnIndex = currentNode.columnIndex,
            distances = 0
        )
    )
    while (cellQueue.isNotEmpty()) {
        val cell = cellQueue.poll()

        val rowIndex = cell.rowIndex
        val columnIndex = cell.columnIndex
        if (rowIndex == nextNode.rowIndex && columnIndex == nextNode.columnIndex) {
            return cell.distances
        }

        // up
        if (rowIndex > 0 && !visited[rowIndex - 1][columnIndex] && forest[rowIndex - 1][columnIndex] != 0) {
            cellQueue.add(
                Cell(
                    rowIndex = rowIndex - 1,
                    columnIndex = columnIndex,
                    distances = cell.distances + 1
                )
            )
            visited[rowIndex - 1][columnIndex] = true
        }

        // right
        if (columnIndex < forest[0].lastIndex && !visited[rowIndex][columnIndex + 1] && forest[rowIndex][columnIndex + 1] != 0) {
            cellQueue.add(
                Cell(
                    rowIndex = rowIndex,
                    columnIndex = columnIndex + 1,
                    distances = cell.distances + 1
                )
            )
            visited[rowIndex][columnIndex + 1] = true
        }

        // down
        if (rowIndex < forest.lastIndex && !visited[rowIndex + 1][columnIndex] && forest[rowIndex + 1][columnIndex] != 0) {
            cellQueue.add(
                Cell(
                    rowIndex = rowIndex + 1,
                    columnIndex = columnIndex,
                    distances = cell.distances + 1
                )
            )
            visited[rowIndex + 1][columnIndex] = true
        }

        // left
        if (columnIndex > 0 && !visited[rowIndex][columnIndex - 1] && forest[rowIndex][columnIndex - 1] != 0) {
            cellQueue.add(
                Cell(
                    rowIndex = rowIndex,
                    columnIndex = columnIndex - 1,
                    distances = cell.distances + 1
                )
            )
            visited[rowIndex][columnIndex - 1] = true
        }
    }

    return -1
}

data class Node(
    val rowIndex: Int,
    val columnIndex: Int,
    val treeHeight: Int
)

data class Cell(
    val rowIndex: Int,
    val columnIndex: Int,
    /**
     * distance from the start node
     */
    val distances: Int
)

fun main() {
    sample_0()
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_0() {
    val forest = listOf(
        listOf(1, 2, 3),
        listOf(0, 0, 4),
        listOf(7, 6, 5)
    )
    val result = cutOffTree(forest)
    assertEquals(6, result)
}

private fun sample_1() {
    val forest = listOf(
        listOf(1, 2, 3),
        listOf(0, 0, 0),
        listOf(7, 6, 5)
    )
    val result = cutOffTree(forest)
    assertEquals(-1, result)
}

private fun sample_2() {
    val forest = listOf(
        listOf(2, 3, 4),
        listOf(0, 0, 5),
        listOf(8, 7, 6)
    )
    val result = cutOffTree(forest)
    assertEquals(6, result)
}

private fun sample_3() {
    val forest = listOf(
        listOf(0),
        listOf(0),
        listOf(6014)
    )
    val result = cutOffTree(forest)
    assertEquals(-1, result)
}

private fun sample_4() {
    val forest = listOf(
        listOf(7, 2, 3),
        listOf(0, 0, 4),
        listOf(1, 6, 5)
    )
    val result = cutOffTree(forest)
    assertEquals(10, result)
}

private fun sample_5() {
    val forest = listOf(
        listOf(54581641, 64080174, 24346381, 69107959),
        listOf(86374198, 61363882, 68783324, 79706116),
        listOf(668150, 92178815, 89819108, 94701471),
        listOf(83920491, 22724204, 46281641, 47531096),
        listOf(89078499, 18904913, 25462145, 60813308)
    )
    val result = cutOffTree(forest)
    assertEquals(57, result)
}
