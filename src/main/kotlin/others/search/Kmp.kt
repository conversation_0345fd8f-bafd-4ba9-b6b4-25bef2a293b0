package others.search

fun main() {
    val text = "aabaacaadaabaaba"
    val pattern = "aaba"
    println(kmp(text, pattern)) // 0, 9, 12
}

private fun kmp(text: String, pattern: String): List<Int> {
    val n = text.length
    val m = pattern.length
    val lps = IntArray(m)
    constructLps(pattern, lps)
    val result = mutableListOf<Int>()
    var i = 0
    var j = 0
    while (i < n) {
       if (text[i] == pattern[j]) {
           i++
           j++
           // store the result if pattern matches
           if (j == m) {
               result.add(i - j)
               // use previous index to skip unnecessary comparisons
               j = lps[j - 1]
           }
       } else {
           if (j != 0) {
               // use previous index to skip unnecessary comparisons
               j = lps[j - 1]
           } else {
               i++
           }
       }
    }
    return result
}

private fun constructLps(pattern: String, lps: IntArray) {
    var len = 0
    lps[0] = 0
    var i = 1
    while (i < pattern.length) {
        if (pattern[i] == pattern[len]) {
            len++
            lps[i] = len
            i++
        } else {
            if (len != 0) {
                // use previous index to skip unnecessary comparisons
                len = lps[len - 1]
            } else {
                lps[i] = 0
                i++
            }
        }
    }
}
