package others.search.binary_search

import kotlin.test.assertEquals

fun main() {
    assertEquals(4, binarySearch(intArrayOf(-1, 0, 3, 5, 9, 12), 9))
    assertEquals(4, binarySearch(intArrayOf(-1, 0, 3, 5, 9, 12, 13), 9))
    assertEquals(-1, binarySearch(intArrayOf(-1, 0, 3, 5, 9, 12), 2))
    assertEquals(0, binarySearch(intArrayOf(5), 5))
    assertEquals(1, binarySearch(intArrayOf(0, 5), 5))
    assertEquals(0, binarySearch(intArrayOf(5, 6), 5))
}

fun binarySearch(nums: IntArray, target: Int): Int {
    if (nums.size == 1) {
        return if (nums[0] == target) 0 else -1
    }

    var startIndex = 0
    var endIndex = nums.lastIndex
    while (startIndex <= endIndex) {
        val index = ((endIndex - startIndex) / 2) + startIndex
        val value = nums[index]
        if (value == target) {
            return index
        } else if (value < target) {
            startIndex = index + 1
        } else {
            endIndex = index - 1
        }
    }
    return -1
}
