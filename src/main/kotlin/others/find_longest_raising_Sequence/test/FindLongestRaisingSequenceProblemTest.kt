package others.find_longest_raising_Sequence.test

import others.assertArrays
import others.find_longest_raising_Sequence.findLongestRaisingSequence

fun main() {
    emptyInput()
    singleElement()
    notFound()

    normal()
    negativeNumber()
    duplicatedNumber()

    multipleSequencesFound()
}

private fun emptyInput() {
    val input = emptyArray<Int>()
    val expectedOutput = emptyArray<Int>()
    verify(input = input, expectedOutput = expectedOutput)
}

private fun singleElement() {
    val input = arrayOf(1)
    val expectedOutput = emptyArray<Int>()
    verify(input = input, expectedOutput = expectedOutput)
}

private fun normal() {
    val input = arrayOf(9, 6, 4, 5, 2, 0)
    val expectedOutput = arrayOf(4, 5)
    verify(input = input, expectedOutput = expectedOutput)
}

private fun negativeNumber() {
    val input = arrayOf(4, 6, -3, 3, 7, 9)
    val expectedOutput = arrayOf(-3, 3, 7, 9)
    verify(input = input, expectedOutput = expectedOutput)
}

private fun duplicatedNumber() {
    val input = arrayOf(1, 0, 0, 0, 1, 2)
    val expectedOutput = arrayOf(0, 1, 2)
    verify(input = input, expectedOutput = expectedOutput)
}

private fun notFound() {
    val input = arrayOf(4, 3, 2, 1)
    val expectedOutput = emptyArray<Int>()
    verify(input = input, expectedOutput = expectedOutput)
}

private fun multipleSequencesFound() {
    val input = arrayOf(1, 2, 3, 0, 4, 5)
    val expectedOutput = arrayOf(1, 2, 3)
    verify(input = input, expectedOutput = expectedOutput)
}

private fun verify(input: Array<Int>, expectedOutput: Array<Int>) {
    val output = findLongestRaisingSequence(input)
    assertArrays(expectedOutput, output)
}