package others.find_longest_raising_Sequence

/**
 * e.g.
 * [4, 6, -3, 3, 7, 9] => [-3, 3, 7, 9]
 * [9, 6, 4, 5, 2, 0] => [4, 5]
 * [4, 3, 5, 8, 5, 0, 0, -3] => [3, 5, 8]
 */
fun findLongestRaisingSequence(input: Array<Int>): Array<Int> {
    if (input.isEmpty()) {
        return emptyArray()
    }
    var startIndex = 0
    var sequenceLength = 1
    var longestStartIndex: Int? = null
    var longestEndIndex: Int? = null
    var longestSequenceLength = 0

    input.forEachIndexed { index, currentNumber ->
        if (index > 0) {
            // skip the first one
            val previousNumber = input[index - 1]
            if (previousNumber < currentNumber) {
                sequenceLength++
            } else {
                if (sequenceLength > longestSequenceLength) {
                    longestSequenceLength = sequenceLength
                    longestStartIndex = startIndex
                    longestEndIndex = index - 1
                }
                startIndex = index
                sequenceLength = 1
            }
        }
    }

    // for the case the longest sequence is including the last element
    if (sequenceLength > longestSequenceLength) {
        longestSequenceLength = sequenceLength
        longestStartIndex = startIndex
        longestEndIndex = input.size - 1
    }

    return if (longestStartIndex != null && longestEndIndex != null) {
        if (longestSequenceLength == 1) {
            emptyArray()
        } else {
            input.copyOfRange(longestStartIndex!!, longestEndIndex!! + 1)
        }
    } else {
        emptyArray()
    }
}
