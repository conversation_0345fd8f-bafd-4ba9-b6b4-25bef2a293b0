package others.breadth_first_travel.test

import others.assertArrays
import others.breadth_first_travel.Node
import others.breadth_first_travel.Tree

fun main() {
    val root = Node(value = 1)
    root.left = Node(value = 2)
    root.right = Node(value = 3)
    root.left?.left = Node(value = 4)
    root.left?.right = Node(value = 5)
    root.left?.left?.left = Node(value = 6)

    val tree = Tree(root = root)
    val result = tree.breadthFirstTravel()
    println(result.joinToString())
    assertArrays(arrayOf(1, 2, 3, 4, 5, 6), result)
}