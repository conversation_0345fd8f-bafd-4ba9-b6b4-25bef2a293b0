package others.breadth_first_travel

import java.util.*

data class Tree(
    val root: Node
) {
    fun breadthFirstTravel(): Array<Int> {
        val result = mutableListOf<Int>()
        val queue = LinkedList<Node>()
        queue.add(root)
        while (queue.isNotEmpty()) {
            val node = queue.pop()
            result.add(node.value)
            node.left?.let {
                queue.add(it)
            }
            node.right?.let {
                queue.add(it)
            }
        }
        return result.toTypedArray()
    }
}

data class Node(
    val value: Int,
    var left: Node? = null,
    var right: Node? = null
)