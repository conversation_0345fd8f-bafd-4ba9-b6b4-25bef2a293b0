package others.sorting.counting

import others.assertArrays

fun main() {
    val unsortedArray = arrayOf(64, 25, 12, 22, 11, 11)
    val expectedSortedArray = unsortedArray.sortedArray()
    val actual = countingSort(unsortedArray)
    assertArrays(expectedSortedArray, actual)
}

// https://www.geeksforgeeks.org/counting-sort/
// https://www.interviewcake.com/concept/java/counting-sort => this is better
fun countingSort(unsortedArray: Array<Int>): Array<Int> {
    val countArray = Array(size = 101) { 0 }
    unsortedArray.forEach { value -> countArray[value]++ }
    for (index in 1..countArray.lastIndex) {
        countArray[index] = countArray[index - 1] + countArray[index]
    }

    val outputArray = Array(size = unsortedArray.size) { 0 }
    unsortedArray.forEach { value ->
        val index = countArray[value] - 1
        outputArray[index] = value
        countArray[value]--
    }
    return outputArray
}

