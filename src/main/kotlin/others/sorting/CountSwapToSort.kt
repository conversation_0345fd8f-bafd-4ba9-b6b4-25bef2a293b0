package others.sorting

fun main() {
    val array = intArrayOf(4, 1, 2, 6, 8, 3)
    val list = mutableListOf<Pair<Int, Int>>()
    for (i in array.indices) {
        list.add(Pair(array[i], i))
    }
    println(
        countSwap(list)
    )
}

private fun countSwap(list: MutableList<Pair<Int, Int>>): Int {
    list.sortWith(compareBy { it.first })
    val visited = BooleanArray(list.size)
    var swap = 0
    for (i in list.indices) {
        if (visited[i] || list[i].second == i) {
            continue
        }
        var cycleSize = 0
        var j = i
        while (!visited[j]) {
            visited[j] = true
            println("swapping $j and ${list[j].second}")
            j = list[j].second
            cycleSize++
        }
        if (cycleSize > 0) swap += cycleSize - 1
    }
    return swap
}