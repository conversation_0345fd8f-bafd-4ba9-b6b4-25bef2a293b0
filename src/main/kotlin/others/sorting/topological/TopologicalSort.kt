package others.sorting.topological

import java.util.*
import kotlin.collections.ArrayList


/**
 * https://www.geeksforgeeks.org/topological-sorting/
 */
fun main() {
    val edges = ArrayList<IntArray>()
    // e.g. 5 -> 0
    edges.add(intArrayOf(5, 0))
    edges.add(intArrayOf(5, 2))
    edges.add(intArrayOf(4, 0))
    edges.add(intArrayOf(4, 1))
    edges.add(intArrayOf(3, 1))
    edges.add(intArrayOf(2, 3))
    // 6, 5, 4, 2, 3, 1, 0

    val numberOfVertex = 7
    val topologicalOrder = topologicalSort(edges, numberOfVertex)
    println("Topological Sort: ${topologicalOrder.joinToString()}")
}

private fun topologicalSort(edges: java.util.ArrayList<IntArray>, numberOfVertex: Int): IntArray {
    val graph = mutableMapOf<Int, MutableSet<Int>>()
    for ((u, v) in edges) {
        graph.computeIfAbsent(u) { mutableSetOf() }.add(v)
    }

    val stack = Stack<Int>()
    val visited = BooleanArray(numberOfVertex)
    for (vertex in 0 until numberOfVertex) {
        if (!visited[vertex]) {
            dfs(graph, vertex, stack, visited)
        }
    }

    val result = IntArray(numberOfVertex)
    var resultIndex = 0
    while (stack.isNotEmpty()) {
        result[resultIndex++] = stack.pop()
    }
    return result
}

private fun dfs(
    graph: MutableMap<Int, MutableSet<Int>>, vertex: Int, stack: Stack<Int>, visited: BooleanArray
) {
    visited[vertex] = true
    for (neighbor in graph.getOrDefault(vertex, emptySet())) {
        if (!visited[neighbor]) {
            dfs(graph, neighbor, stack, visited)
        }
    }
    stack.push(vertex)
}
