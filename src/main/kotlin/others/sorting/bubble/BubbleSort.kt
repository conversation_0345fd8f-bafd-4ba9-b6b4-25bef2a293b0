package others.sorting.bubble

import others.assertArrays

fun main() {
    val unsortedArray = arrayOf(64, 25, 12, 22, 11, 11)
    val expectedSortedArray = unsortedArray.sortedArray()
    val actual = bubbleSort(unsortedArray)
    assertArrays(expectedSortedArray, actual)
}

// https://www.geeksforgeeks.org/bubble-sort/
private fun bubbleSort(unsortedArray: Array<Int>): Array<Int> {
    val sortedArray = unsortedArray.clone()
    val startIndex = 0
    var endIndex = sortedArray.size - 1
    while (startIndex < endIndex) {
        for (index in startIndex until endIndex) {
            val firstValue = sortedArray[index]
            val nextValue = sortedArray[index + 1]
            if (firstValue > nextValue) {
                sortedArray[index] = nextValue
                sortedArray[index + 1] = firstValue
            }
        }
        // process the next slot
        endIndex--
    }
    return sortedArray
}
