package others.sorting.insertion

import others.assertArrays

fun main() {
    val unsortedArray = arrayOf(64, 25, 12, 22, 11, 11)
    val expectedSortedArray = unsortedArray.sortedArray()
    val actual = insertionSort(unsortedArray)
    assertArrays(expectedSortedArray, actual)
}

// https://www.geeksforgeeks.org/insertion-sort/
private fun insertionSort(unsortedArray: Array<Int>): Array<Int> {
    val sortedArray = unsortedArray.clone()
    val endIndex = sortedArray.size - 1
    for (index in 1..endIndex) {
        val value = sortedArray[index]
        var currentIndex = index
        var indexToCheck = index - 1
        while (indexToCheck >= 0) {
            val valueToCheck = sortedArray[indexToCheck]
            if (value < valueToCheck) {
                sortedArray[currentIndex] = valueToCheck
                sortedArray[indexToCheck] = value

                currentIndex = indexToCheck
            }
            indexToCheck--
        }
    }
    return sortedArray
}

