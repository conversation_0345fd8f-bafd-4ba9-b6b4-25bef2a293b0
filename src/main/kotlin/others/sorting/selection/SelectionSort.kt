package others.sorting.selection

import others.assertArrays

fun main() {
    val unsortedArray = arrayOf(64, 25, 12, 22, 11, 11)
    val expectedSortedArray = unsortedArray.sortedArray()
    val actual = selectionSort(unsortedArray)
    assertArrays(expectedSortedArray, actual)
}

// https://www.geeksforgeeks.org/selection-sort/
private fun selectionSort(unsortedArray: Array<Int>): Array<Int> {
    val sortedArray = unsortedArray.clone()
    var indexToProcess = 0
    val endIndex = sortedArray.size - 1
    while (indexToProcess <= endIndex) {
        val valueToCheck = sortedArray[indexToProcess]
        // find the min value
        var minValue = valueToCheck
        var indexOfMinValue = indexToProcess
        for (index in indexToProcess + 1..endIndex) {
            val value = sortedArray[index]
            if (value < minValue) {
                minValue = value
                indexOfMinValue = index
            }
        }
        // swap the value
        if (minValue < valueToCheck) {
            sortedArray[indexToProcess] = minValue
            sortedArray[indexOfMinValue] = valueToCheck
        }
        // process the next slot
        indexToProcess++
    }
    return sortedArray
}