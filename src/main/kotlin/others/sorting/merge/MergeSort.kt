package others.sorting.merge

import others.assertArrays

fun main() {
    val unsortedArray = arrayOf(64, 25, 12, 22, 11, 11)
    val expectedSortedArray = unsortedArray.sortedArray()
    val actual = mergeSort(unsortedArray)
    assertArrays(expectedSortedArray, actual)
}

// https://www.geeksforgeeks.org/merge-sort/
private fun mergeSort(unsortedArray: Array<Int>): Array<Int> {
    val sortedArray = unsortedArray.clone()
    internalMergeSort(sortedArray, startIndex = 0, endIndex = unsortedArray.lastIndex)
    return sortedArray
}

private fun internalMergeSort(unsortedArray: Array<Int>, startIndex: Int, endIndex: Int) {
    if (endIndex > startIndex) {
        val middleIndex = (startIndex + endIndex) / 2
        internalMergeSort(unsortedArray, startIndex, middleIndex)
        internalMergeSort(unsortedArray, middleIndex + 1, endIndex)
        merge(unsortedArray, startIndex, middleIndex, endIndex)
    }
}

private fun merge(unsortedArray: Array<Int>, startIndex: Int, middleIndex: Int, endIndex: Int) {
    val smallerArray = unsortedArray.sliceArray(startIndex..middleIndex)
    val largerArray = unsortedArray.sliceArray(middleIndex + 1..endIndex)

    var smallerArrayIndex = 0
    var largerArrayIndex = 0
    var index = startIndex
    while (smallerArrayIndex <= smallerArray.lastIndex && largerArrayIndex <= largerArray.lastIndex) {
        val valueInSmallerArray = smallerArray[smallerArrayIndex]
        val valueInLargerArray = largerArray[largerArrayIndex]
        if (valueInSmallerArray <= valueInLargerArray) {
            unsortedArray[index++] = valueInSmallerArray
            smallerArrayIndex++
        } else {
            unsortedArray[index++] = valueInLargerArray
            largerArrayIndex++
        }
    }

    while (smallerArrayIndex <= smallerArray.lastIndex) {
        unsortedArray[index++] = smallerArray[smallerArrayIndex++]
    }
    while (largerArrayIndex <= largerArray.lastIndex) {
        unsortedArray[index++] = largerArray[largerArrayIndex++]
    }
}