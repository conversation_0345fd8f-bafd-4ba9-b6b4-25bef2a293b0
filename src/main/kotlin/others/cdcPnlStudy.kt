package others

import java.math.BigDecimal
import java.util.LinkedList

fun main() {
    val averagePricePosition = AveragePricePosition("BTC")
    averagePricePosition.add(BuyOrder(60000.0, 1.0))
    averagePricePosition.add(BuyOrder(55000.0, 2.0))
    averagePricePosition.add(SellOrder(61000.0, 2.0))
    averagePricePosition.add(SellOrder(61000.0, 2.0))
}

private class AveragePricePosition(s: String): Position(s) {
    private var averagePrice: BigDecimal = BigDecimal.ZERO

    override fun add(order: Order) {
        println("adding $order")
        val absOrderQty = order.qty.abs()
        val isReducePosition = isReducePosition(order)
        var flipSide = false
        if (isReducePosition) {
            val reducedQty = if (absOrderQty > qty) {
                flipSide = true
                qty
            } else {
                absOrderQty
            }
            val pnl = (averagePrice - order.price).abs() * reducedQty
            println("position is reduced by $reducedQty and generated PnL: $pnl")
        }
        averagePrice = if ((qty + order.qty).compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal.ZERO
        } else if (flipSide) {
            order.price
        } else if (!isReducePosition) {
            ((averagePrice * qty + order.price * order.qty) / (qty + order.qty)).abs()
        } else {
            averagePrice
        }
        qty += order.qty
        println("$this\n")
    }

    override fun toString(): String {
        return "AveragePricePosition(symbol='$symbol', averagePrice=$averagePrice, qty=$qty)"
    }
}

private class FifoPosition(s: String): Position(s) {
    private val openTrades = LinkedList<Order>()

    override fun add(order: Order) {
        TODO("Not yet implemented")
    }
}

private abstract class Position(val symbol: String) {
    protected var qty: BigDecimal = BigDecimal.ZERO

    abstract fun add(order: Order)

    protected fun isReducePosition(order: Order) = if (qty > BigDecimal.ZERO) {
        qty + order.qty < qty
    } else if (qty < BigDecimal.ZERO) {
        qty + order.qty > qty
    } else {
        false
    }
}

private class BuyOrder(p: Double, q: Double) : Order(p, q, true)
private class SellOrder(p: Double, q: Double) : Order(p, q, false)
private abstract class Order(p: Double, q: Double, val isBuy: Boolean) {
    val price = p.toBigDecimal()
    val qty: BigDecimal = if (isBuy) q.toBigDecimal() else q.toBigDecimal().negate()

    override fun toString(): String {
        return "Order(price: $price, qty: $qty)"
    }
}