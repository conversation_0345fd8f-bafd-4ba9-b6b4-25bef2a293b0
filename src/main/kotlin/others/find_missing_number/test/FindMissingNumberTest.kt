package others.find_missing_number.test

import others.find_missing_number.findMissingNumber
import kotlin.test.assertEquals

fun main() {
    sample_0()
    sample_1()
}

private fun sample_0() {
    val input = "11122333"
    val output = findMissingNumber(input)
    val expectedOutput = "2"
    assertEquals(expectedOutput, output)
}

private fun sample_1() {
    val input = "11122233344455666"
    val output = findMissingNumber(input)
    val expectedOutput = "5"
    assertEquals(expectedOutput, output)
}

