package others.find_missing_number

/**
 * Given a contiguous sequence of numbers in which each number repeats thrice,
 * there is exactly one missing number. Find the missing number.
 * eg: 11122333 : Missing number 2
 * 11122233344455666 Missing number 5
 */
fun findMissingNumber(s: String): String {
    var index = 0
    while (index <= s.lastIndex) {
        val character = s[index]
        val expectedLastSameCharacter = s[index + 2]
        if (character != expectedLastSameCharacter) {
            return character.toString()
        } else {
            index += 3
        }
    }
    return s[s.lastIndex].toString()
}