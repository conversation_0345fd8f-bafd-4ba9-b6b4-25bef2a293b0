package others.count_way_to_reach_n_stair

/*
  There are n stairs, a person standing at the bottom wants to reach the top.
  The person can climb either 1 stair or 2 stairs at a time.
  Count the number of ways, the person can reach the top.
 */

fun countWayToReachStair(stairCount: Int): Int {
    val ways = IntArray(stairCount + 1) { 0 }

    for (index in 0..stairCount) {
        if (index <= 2) {
            ways[index] = index
        } else if (stairCount >= index) {
            ways[index] = ways[index - 2] + ways[index - 1]
        }
    }

    return ways[stairCount]

}