package others.count_way_to_reach_n_stair.test

import others.count_way_to_reach_n_stair.countWayToReachStair
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
}

private fun sample_1() {
    verify(
        stairCount = 1,
        expected = 1
    )
}

private fun sample_2() {
    verify(
        stairCount = 2,
        expected = 2
    )
}

private fun sample_3() {
    verify(
        stairCount = 4,
        expected = 5
    )
}

private fun sample_4() {
    verify(
        stairCount = 0,
        expected = 0
    )
}

private fun sample_5() {
    verify(
        stairCount = 3,
        expected = 3
    )
}

private fun verify(stairCount: Int, expected: Int) {
    val actual = countWayToReachStair(stairCount)
    assertEquals(expected, actual)
}