package others

private val MILLI_START = System.currentTimeMillis()
private val NANO_START = System.nanoTime()
private val EPOCH_NANOS = MILLI_START * 1000000
private val OFFSET_NANOS = EPOCH_NANOS - NANO_START
class TimeGenerator {
    fun nanoTime() = System.nanoTime() + OFFSET_NANOS

    fun millTime() = System.currentTimeMillis()
}

fun main() {
    val timeGenerator = TimeGenerator()
    repeat(10) {
        println("${timeGenerator.millTime()} ms and ${timeGenerator.nanoTime()} ns")
        Thread.sleep(1000)
    }
}