package others.find_recurring_sequence_in_a_fraction.test

import others.find_recurring_sequence_in_a_fraction.findRecurringSequenceInFraction
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
}

private fun sample_1() {
    verify(
        numerator = 8,
        denominator = 4,
        expected = 0
    )
}

private fun sample_2() {
    verify(
        numerator = 8,
        denominator = 3,
        expected = 6
    )
}

private fun sample_3() {
    verify(
        numerator = 50,
        denominator = 22,
        expected = 27
    )
}

private fun sample_4() {
    verify(
        numerator = 5,
        denominator = 7,
        expected = 714285
    )
}

private fun sample_5() {
    verify(
        numerator = 11,
        denominator = 2,
        expected = 0
    )
}

private fun sample_6() {
    verify(
        numerator = 0,
        denominator = 2,
        expected = 0
    )
}

private fun verify(numerator: Int, denominator: Int, expected: Int) {
    val actual = findRecurringSequenceInFraction(numerator, denominator)
    assertEquals(expected, actual)
}
