package others.find_recurring_sequence_in_a_fraction

import kotlin.math.pow

/**
 * Given a fraction, find recurring sequence of digits if exists, otherwise print “No recurring sequence”.
 */
fun findRecurringSequenceInFraction(numerator: Int, denominator: Int): Int {
    val reminder = numerator % denominator
    val newNumerators = mutableListOf<Int>()
    var newNumerator = reminder * 10
    while (newNumerator != 0 && !newNumerators.contains(newNumerator)) {
        newNumerators.add(newNumerator)
        newNumerator = (newNumerator % denominator) * 10
    }

    val lastIndex = newNumerators.size - 1
    val indexOfDuplicatedNumerator = newNumerators.indexOf(newNumerator)
    val newNumeratorFactor = 10.0.pow((lastIndex - indexOfDuplicatedNumerator).toDouble())
    newNumerator = (newNumerator * newNumeratorFactor).toInt()

    return newNumerator / denominator
}