package goldman_sachs.min_coin_change

import java.util.*

// https://leetcode.com/problems/coin-change/

private const val minCoinsDoesNotExist = Int.MAX_VALUE

fun findMinCoins(target: Int, coins: IntArray): Int {
    val minCoins = IntArray(target + 1) { minCoinsDoesNotExist }
    minCoins[0] = 0

    for (coin in coins) {
        for (value in coin..target) {
            val subMinCoin = minCoins[value - coin]
            if (subMinCoin != minCoinsDoesNotExist) {
                val newMinCoin = 1 + subMinCoin
                if (newMinCoin < minCoins[value]) {
                    minCoins[value] = newMinCoin
                }
            }
        }
    }

    val result = minCoins[target]
    return if (result == minCoinsDoesNotExist) 0 else result
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)

    val value = scan.nextLine().trim().toInt()
    val coins = scan.nextLine()
        .split(",")
        .map { it.trim().toInt() }
        .toIntArray()

    val result = findMinCoins(value, coins)

    println(result)
}