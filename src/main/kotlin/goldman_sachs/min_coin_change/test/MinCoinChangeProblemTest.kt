package goldman_sachs.min_coin_change.test

import hackerrank.practice.util.testTemplate
import goldman_sachs.min_coin_change.main as mainFunction

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
}

fun sample_1() {
    val inputs = "30\n" +
            "25, 10, 5"
    val expectedOutput = "2"
    testTemplate("sample_1", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_2() {
    val inputs = "11\n" +
            "9, 6, 5, 1"
    val expectedOutput = "2"
    testTemplate("sample_2", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_3() {
    val inputs = "47\n" +
            "1, 2, 5, 10"
    val expectedOutput = "6"
    testTemplate("sample_3", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_4() {
    val inputs = "27\n" +
            "1, 2, 5, 10, 50"
    val expectedOutput = "4"
    testTemplate("sample_4", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_5() {
    val inputs = "5\n" +
            "7, 10, 6"
    val expectedOutput = "0"
    testTemplate("sample_5", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}

fun sample_6() {
    val inputs = "10\n" +
            "7, 10, 6"
    val expectedOutput = "1"
    testTemplate("sample_6", inputs, expectedOutput) { args ->
        mainFunction(args)
    }
}
