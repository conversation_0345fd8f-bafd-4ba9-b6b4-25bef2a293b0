package goldman_sachs.hashmap.test

import goldman_sachs.hashmap.MakeHashMap
import kotlin.test.assertEquals

fun main() {
    // test empty map
    val map = MakeHashMap<String, String>()
    assertEquals(0, map.size())
    assertEquals(null, map.get("any non exist key"))

    // test simple put and get
    map.put("key_1", "value_1")
    assertEquals(1, map.size())
    assertEquals(null, map.get("any non exist key"))
    assertEquals("value_1", map.get("key_1"))

    // test collision
    for (i in 2..17) {
        val key = "key_$i"
        val value = "value_$i"
        map.put(key, value)
        assertEquals(i, map.size())
        assertEquals(value, map.get(key))
    }

    // test null key
    map.put(null, "value of null key")
    assertEquals("value of null key", map.get(null))
    assertEquals(18, map.size())

    // test null value
    map.put("key for null value", null)
    assertEquals(null, map.get("key for null value"))
    assertEquals(19, map.size())

    // test replace value
    map.put("key for testing replace value", "original value")
    assertEquals("original value", map.get("key for testing replace value"))
    assertEquals(20, map.size())
    map.put("key for testing replace value", "updated value")
    assertEquals("updated value", map.get("key for testing replace value"))
    assertEquals(20, map.size())

    // test the map will not return wrong value
    for (i in 18..33) {
        val key = "key_$i"
        assertEquals(null, map.get(key))
    }
}

