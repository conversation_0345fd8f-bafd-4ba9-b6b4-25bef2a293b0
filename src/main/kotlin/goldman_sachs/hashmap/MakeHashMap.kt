package goldman_sachs.hashmap

import kotlin.math.absoluteValue

class MakeHashMap<K, V> {
    private val initialSize = 16 // should be a number of 2^x
    private val buckets = Array<Entry<K, V>?>(initialSize) { null }
    private var size = 0

    fun size(): Int {
        return size
    }

    fun put(key: K?, value: V?) {
        val bucketsIndex = findBucketIndex(key)
        val existingEntry = buckets[bucketsIndex]

        existingEntry?.let {
            val entry = findOrLast(it, key)
            if (entry.key == key) {
                entry.value = value
            } else {
                entry.next = createEntry(key, value)
                size++
            }
        } ?: run {
            buckets[bucketsIndex] = createEntry(key, value)
            size++
        }
    }

    fun get(key: K?): V? {
        val bucketsIndex = findBucketIndex(key)

        return buckets[bucketsIndex]?.let {
            val entry = findOrLast(it, key)
            return if (entry.key == key) {
                entry.value
            } else {
                null
            }
        }
    }

    /**
     * Find the entry matched the key or the last entry
     */
    private fun findOrLast(firstEntry: Entry<K, V>, key: K?): Entry<K, V> {
        var entry = firstEntry
        while (entry.key != key && entry.next != null) {
            entry.next?.let { nextEntry ->
                entry = nextEntry
            }
        }
        return entry
    }

    private fun findBucketIndex(key: K?): Int {
        // TODO: use bitwise operation is faster
        val hashCode = key.hashCode().absoluteValue
        return hashCode % initialSize
    }

    private fun createEntry(key: K?, value: V?) = Entry(key, value, next = null)

    private data class Entry<K, V>(
        val key: K?,
        var value: V?,
        var next: Entry<K, V>?
    )
}
