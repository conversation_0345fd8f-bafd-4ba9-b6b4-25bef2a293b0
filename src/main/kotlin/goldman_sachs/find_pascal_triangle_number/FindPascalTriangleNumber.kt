package goldman_sachs.find_pascal_triangle_number

/**
 * 1
 * 1 1
 * 1 2 1
 * 1 3 3 1
 * 1 4 6 4 1
 * 1 5 10 10 5 1
 *
 * C(n, k) = C(n-1, k-1) + C(n-1, k)
 * C(n, 0) = C(n, n) = 1
 */
fun findPascalTriangleNumber(x: Int, y: Int): Int {
    if (y == 0) {
        return 1
    }

    val C = Array(y + 1) { IntArray(x + 1) { 0 } }
    for (n in 1..y) {
        for (k in 0..x) {
            if (k == 0 || n == k) {
                C[n][k] = 1
            } else {
                C[n][k] = C[n - 1][k - 1] + C[n - 1][k]
            }
        }
    }

    return C[y][x]
}