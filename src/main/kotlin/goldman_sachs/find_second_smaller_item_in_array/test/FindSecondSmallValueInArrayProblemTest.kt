package goldman_sachs.find_second_smaller_item_in_array.test

import goldman_sachs.find_second_smaller_item_in_array.findSecondSmallerItem
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
}

private fun sample_1() {
    verify(
        array = arrayOf(1, 3, 5, 10, 4),
        expectedResult = 3
    )
}

private fun sample_2() {
    verify(
        array = arrayOf(1, 2, 3, 4, 5),
        expectedResult = 2
    )
}

private fun sample_3() {
    verify(
        array = emptyArray(),
        expectedResult = null
    )
}

private fun sample_4() {
    verify(
        array = arrayOf(1),
        expectedResult = 1
    )
}

private fun sample_5() {
    verify(
        array = arrayOf(1, 1, 1, 1),
        expectedResult = 1
    )
}

private fun sample_6() {
    verify(
        array = arrayOf(5, 4, 3, 2, 1),
        expectedResult = 2
    )
}

private fun verify(array: Array<Int>, expectedResult: Int?) {
    val actualResult = findSecondSmallerItem(array)
    assertEquals(expectedResult, actualResult)
}
