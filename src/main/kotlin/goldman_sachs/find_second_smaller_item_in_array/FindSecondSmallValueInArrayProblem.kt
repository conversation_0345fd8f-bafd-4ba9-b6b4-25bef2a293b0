package goldman_sachs.find_second_smaller_item_in_array

fun findSecondSmallerItem(array: Array<Int>): Int? {
    when {
        array.isEmpty() -> {
            return null
        }
        array.size == 1 -> {
            return array[0]
        }
        else -> {
            var smallest = Int.MAX_VALUE
            var secondSmallest = Int.MAX_VALUE

            array.forEach { value ->
                if (value < smallest) {
                    secondSmallest = smallest
                    smallest = value
                } else if (value < secondSmallest) {
                    secondSmallest = value
                }
            }

            return secondSmallest
        }
    }
}