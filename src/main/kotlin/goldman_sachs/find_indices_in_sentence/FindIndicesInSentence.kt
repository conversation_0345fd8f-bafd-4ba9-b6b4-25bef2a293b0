package goldman_sachs.find_indices_in_sentence

/**
 * You are given a document eg. ("An apple a day is good but two apples are better"),
 * and a prefix eg. ("ap").
 * You need to return the list of indices where this prefix occurs in the document,
 * in this case [3,31] in an efficient manner.
 */

fun findIndicesInSentence(sentence: String, prefix: String): List<Int> {
    val result = mutableListOf<Int>()

    var prefixIndex = 0
    var matchedIndex = 0
    val prefixEndIndex = prefix.lastIndex
    for (charIndex in sentence.indices) {
        val character = sentence[charIndex]
        val prefixChar = prefix[prefixIndex]
        if (character == prefixChar) {
            if (prefixIndex == 0) {
                matchedIndex = charIndex
            }

            if (prefixIndex == prefixEndIndex) {
                result.add(matchedIndex)
                matchedIndex = 0
                prefixIndex = 0
            } else {
                prefixIndex++
            }
        } else {
            prefixIndex = 0
            matchedIndex = 0
        }
    }
    return result
}
