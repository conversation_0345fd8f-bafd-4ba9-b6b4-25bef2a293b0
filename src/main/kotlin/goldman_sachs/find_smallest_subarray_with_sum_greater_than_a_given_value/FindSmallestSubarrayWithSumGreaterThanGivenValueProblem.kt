package goldman_sachs.find_smallest_subarray_with_sum_greater_than_a_given_value

/**
 * Given an array of integers and a number x, find the smallest subarray with sum greater than the given value.
 *
 * https://leetcode.com/problems/minimum-size-subarray-sum/
 *
 * However, I modified it to make it work for negative number
 */
fun findSmallestSubarrayWithSumGreaterThanGivenValue(nums: IntArray, target: Int): Int {
    var smallest = Int.MAX_VALUE
    var startIndex = 0
    var sum = 0
    for (index in nums.indices) {
        val currentValue = nums[index]
        if (currentValue >= target) {
            return 1
        } else if (currentValue < 0) {
            continue
        }

        sum += currentValue
        while (sum >= target) {
            val count = index - startIndex + 1
            if (count < smallest) {
                smallest = count
            }
            if (nums[startIndex] > 0) {
                sum -= nums[startIndex]
            }
            startIndex++
        }
    }

    return if (smallest == Int.MAX_VALUE) 0 else smallest
}