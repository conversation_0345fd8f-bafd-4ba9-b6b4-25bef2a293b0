package goldman_sachs.find_smallest_subarray_with_sum_greater_than_a_given_value.test

import goldman_sachs.find_smallest_subarray_with_sum_greater_than_a_given_value.findSmallestSubarrayWithSumGreaterThanGivenValue
import kotlin.test.assertEquals

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
}

private fun sample_1() {
    verify(
        array = intArrayOf(1, 4, 45, 6, 0, 19),
        givenValue = 51,
        expected = 2
    )
}

private fun sample_2() {
    verify(
        array = intArrayOf(1, 10, 5, 2, 7),
        givenValue = 9,
        expected = 1
    )
}

private fun sample_3() {
    verify(
        array = intArrayOf(1, 11, 100, 1, 0, 200, 3, 2, 1, 250),
        givenValue = 280,
        expected = 4
    )
}

private fun sample_4() {
    verify(
        array = intArrayOf(1, 2, 4),
        givenValue = 8,
        expected = 0
    )
}

private fun sample_5() {
    verify(
        array = intArrayOf(0, 19, 1, 4, 45, 6),
        givenValue = 51,
        expected = 2
    )
}

private fun sample_6() {
    verify(
        array = intArrayOf(-8, 1, 4, 2, -6),
        givenValue = 6,
        expected = 2
    )
}

private fun sample_7() {
    verify(
        array = intArrayOf(-8, 2, 5, -6),
        givenValue = -3,
        expected = 1
    )
}

private fun sample_8() {
    verify(
        array = intArrayOf(-10, -7, -2, -1),
        givenValue = -3,
        expected = 1
    )
}

private fun verify(array: IntArray, givenValue: Int, expected: Int) {
    val actual = findSmallestSubarrayWithSumGreaterThanGivenValue(array, givenValue)
    assertEquals(expected, actual)
}
