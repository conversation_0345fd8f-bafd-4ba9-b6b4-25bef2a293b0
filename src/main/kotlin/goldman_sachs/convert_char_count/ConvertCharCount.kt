package goldman_sachs.convert_char_count

/**
 * implement the run length coding for a given string. example user input aaabbc, output should be a3b2c1
 */

fun convertCharCount(s: String): String {
    val result = StringBuilder()

    var currentChar = s[0]
    var charCount = 1
    for (index in 1 until s.length) {
        val character = s[index]
        if (character == currentChar) {
            charCount++
        } else {
            result.append(currentChar).append(charCount)
            currentChar = character
            charCount = 1
        }
    }

    result.append(currentChar).append(charCount)

    return result.toString()
}