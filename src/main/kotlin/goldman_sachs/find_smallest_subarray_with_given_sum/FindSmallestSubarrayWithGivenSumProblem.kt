package goldman_sachs.find_smallest_subarray_with_given_sum

/**
 * Given an unsorted array of integers, find a continuous subarray which adds to a given number.
 *
 * Similar to https://www.geeksforgeeks.org/find-subarray-with-given-sum/amp/ but we return the sub-array
 *
 * And it is supporting -ve number
 */
fun findSmallestSubarrayWithGivenSum(nums: IntArray, target: Int): IntArray {
    var smallestSize = Int.MAX_VALUE
    var startIndex = 0
    var smallestStartIndex = 0
    var smallestEndIndex = 0
    var sum = 0

    for (index in nums.indices) {
        val value = nums[index]
        if (value == target) {
            return intArrayOf(value)
        }

        sum += value
        if (target >= 0) {
            while (sum > target) {
                sum -= nums[startIndex]
                startIndex++
            }
        } else {
            while (sum < target) {
                sum -= nums[startIndex]
                startIndex++
            }
        }

        if (sum == target) {
            val size = index - startIndex + 1
            if (size < smallestSize) {
                smallestStartIndex = startIndex
                smallestEndIndex = index
                smallestSize = size
            }
        }
    }
    return if (smallestSize == Int.MAX_VALUE) {
        intArrayOf()
    } else {
        nums.copyOfRange(smallestStartIndex, smallestEndIndex + 1)
    }
}