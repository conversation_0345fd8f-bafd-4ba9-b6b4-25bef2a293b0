package goldman_sachs.find_smallest_subarray_with_given_sum.test

import others.assertArrays
import goldman_sachs.find_smallest_subarray_with_given_sum.findSmallestSubarrayWithGivenSum

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
    sample_9()
}

private fun sample_1() {
    verify(
        array = intArrayOf(1, 4, 20, 3, 10, 5),
        givenValue = 33,
        expected = intArrayOf(20, 3, 10)
    )
}

private fun sample_2() {
    verify(
        array = intArrayOf(1, 4, 0, 0, 3, 10, 5),
        givenValue = 7,
        expected = intArrayOf(4, 0, 0, 3)
    )
}

private fun sample_3() {
    verify(
        array = intArrayOf(1, 4),
        givenValue = 0,
        expected = intArrayOf()
    )
}

private fun sample_4() {
    verify(
        array = intArrayOf(1, 4, 2, 0),
        givenValue = 0,
        expected = intArrayOf(0)
    )
}

private fun sample_5() {
    verify(
        array = intArrayOf(1, 4, 0, 0, 3, 7, 5),
        givenValue = 7,
        expected = intArrayOf(7)
    )
}

private fun sample_6() {
    verify(
        array = intArrayOf(1, 3, 4, 0, 0, 3, 9),
        givenValue = 7,
        expected = intArrayOf(3, 4)
    )
}

private fun sample_7() {
    verify(
        array = intArrayOf(1, -1, 3, 4, 5),
        givenValue = 6,
        expected = intArrayOf(-1, 3, 4)
    )
}

private fun sample_8() {
    verify(
        array = intArrayOf(10, 2, -2, -20, 10),
        givenValue = -10,
        expected = intArrayOf(10, 2, -2, -20)
    )
}

private fun sample_9() {
    verify(
        array = intArrayOf(-10, 0, 2, -2, -20, 10),
        givenValue = 20,
        expected = intArrayOf()
    )
}

private fun verify(array: IntArray, givenValue: Int, expected: IntArray) {
    val actual = findSmallestSubarrayWithGivenSum(array, givenValue)
    assertArrays(expected.toTypedArray(), actual.toTypedArray())
}
