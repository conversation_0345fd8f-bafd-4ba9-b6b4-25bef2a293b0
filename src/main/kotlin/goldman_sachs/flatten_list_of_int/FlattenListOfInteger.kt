package goldman_sachs

import others.assertArrays
import java.util.*

/**
 * // This is the interface that allows for creating nested lists.
 * // You should not implement it, or speculate about its implementation
 * class NestedInteger {
 *     // Constructor initializes an empty nested list.
 *     constructor()
 *
 *     // Constructor initializes a single integer.
 *     constructor(value: Int)
 *
 *     // @return true if this NestedInteger holds a single integer, rather than a nested list.
 *     fun isInteger(): Boolean
 *
 *     // @return the single integer that this NestedInteger holds, if it holds a single integer
 *     // Return null if this NestedInteger holds a nested list
 *     fun getInteger(): Int?
 *
 *     // Set this NestedInteger to hold a single integer.
 *     fun setInteger(value: Int): Unit
 *
 *     // Set this NestedInteger to hold a nested list and adds a nested integer to it.
 *     fun add(ni: NestedInteger): Unit
 *
 *     // @return the nested list that this NestedInteger holds, if it holds a nested list
 *     // Return null if this NestedInteger holds a single integer
 *     fun getList(): List<NestedInteger>?
 * }
 */
class NestedIterator(nestedList: List<NestedInteger>) {
    private val integers: List<Int>
    private var index = 0

    init {
        integers = mutableListOf()

        val stack = Stack<NestedInteger>()
        for (item in nestedList.asReversed()) {
            stack.push(item)
        }

        while (stack.isNotEmpty()) {
            val item = stack.pop()
            if (item.isInteger()) {
                integers.add(item.integer!!)
            } else {
                for (nestedItem in item.list.asReversed()) {
                    stack.push(nestedItem)
                }
            }
        }
    }

    fun next(): Int {
        return integers[index++]
    }

    fun hasNext(): Boolean {
        return index < integers.size
    }
}

data class NestedInteger(
    val integer: Int? = null,
    val list: List<NestedInteger> = mutableListOf()
) {
    fun isInteger(): Boolean {
        return integer != null
    }
}

fun main() {
    /*
     * Input: [[1,2],3,[4,5]]
     * Output: [1, 2, 3, 4, 5]
     */
    var nestedList = mutableListOf<NestedInteger>()
    nestedList.add(NestedInteger(list = listOf(NestedInteger(integer = 1), NestedInteger(integer = 2))))
    nestedList.add(NestedInteger(integer = 3))
    nestedList.add(NestedInteger(list = listOf(NestedInteger(integer = 4), NestedInteger(integer = 5))))

    var list = NestedIterator(nestedList)

    var result = mutableListOf<Int>()
    while (list.hasNext()) {
        result.add(list.next())
    }
    println(result)
    assertArrays(arrayOf(1, 2, 3, 4, 5), result.toTypedArray())

    /*
     * Input: [[[8],4]]
     * Output: [8,4]
     */
    nestedList = mutableListOf()
    nestedList.add(
        NestedInteger(
            list = listOf(
                NestedInteger(
                    list = listOf(
                        NestedInteger(
                            list = listOf(
                                NestedInteger(list = listOf(NestedInteger(integer = 8)))
                            )
                        ),
                        NestedInteger(integer = 4)
                    )
                )
            )
        )
    )

    list = NestedIterator(nestedList)

    result = mutableListOf()
    while (list.hasNext()) {
        result.add(list.next())
    }
    println(result)
    assertArrays(arrayOf(8, 4), result.toTypedArray())
}