package google.people_in_room.test

import hackerrank.practice.util.sampleTemplate
import google.people_in_room.main as mainFunction

fun main() {
    sample_1()
    sample_2()
    sample_3()
    sample_4()
    sample_5()
    sample_6()
    sample_7()
    sample_8()
    sample_9()
    sample_10()
}

private fun sample_1() {
    sample(
        sampleNumber = 1,
        inputs = "1\n" +
                "1",
        expectedOutput = "1",
    )
}

private fun sample_2() {
    sample(
        sampleNumber = 2,
        inputs = "1\n" +
                "1 0",
        expectedOutput = "1",
    )
}

private fun sample_3() {
    sample(
        sampleNumber = 3,
        inputs = "1\n" +
                "1 0 1",
        expectedOutput = "2",
    )
}

private fun sample_4() {
    sample(
        sampleNumber = 4,
        inputs = "1\n" +
                "1 0 1 0",
        expectedOutput = "2",
    )
}

private fun sample_5() {
    sample(
        sampleNumber = 5,
        inputs = "1\n" +
                "1 0 0 0 1",
        expectedOutput = "4",
    )
}

private fun sample_6() {
    sample(
        sampleNumber = 6,
        inputs = "2\n" +
                "1",
        expectedOutput = "-1",
    )
}

private fun sample_7() {
    sample(
        sampleNumber = 7,
        inputs = "2\n" +
                "1 0 0",
        expectedOutput = "-1",
    )
}

private fun sample_8() {
    sample(
        sampleNumber = 8,
        inputs = "2\n" +
                "1 0 1 0 0 0 1 0 0 1 0",
        expectedOutput = "4",
    )
}

private fun sample_9() {
    sample(
        sampleNumber = 9,
        inputs = "2\n" +
                "1 0 1 1 0 1",
        expectedOutput = "1",
    )
}

private fun sample_10() {
    sample(
        sampleNumber = 9,
        inputs = "2\n" +
                "0 1 0 1 1 0 1",
        expectedOutput = "1",
    )
}

private fun sample(sampleNumber: Int, inputs: String, expectedOutput: String) {
    sampleTemplate(
        sampleNumber = sampleNumber,
        inputs = inputs,
        expectedOutput = expectedOutput,
        mainFunction = ::mainFunction
    )
}