package google.people_in_room

import java.util.*

/**
 * It is a google internship interview question
 *
 * The hostel administration of your college wants to keep exactly k students in one room.
 * There are n numbers of beds represented by a string s that includes only 0 and 1.
 * Here 1 denotes a student is present on the ith bed and 0 denotes that there is no student on the ith bed.
 *
 * Your task is to determine the possible ways to build a wall so that only k students are present in one room.
 * Since the answer can be very large, print it modulo 10^9 + 7.
 * If there is no way to fulfill the mentioned condition, then print "-1" (without quote)
 *
 * Input format
 * - The first line contains an integer t denoting the number of tests cases.
 * - The first line of each test case contains two space-separated integers n and k
 * - The second line of each test case has a string s denoting the configuration beds
 *
 * Output format
 * Print the number of possible ways 10^9 + 7 such that each room has exactly k people.
 * Otherwise, print "-1" (without quote)
 *
 * Constraints
 * 1 <= t <= 10
 * 1 <= n <= 10^6
 * 1 <= k <= n
 * s consists of only two characters 0 and 1
 */

private const val MOD = 1000000007

fun findWays(k: Int, s: IntArray): Int {
    var studentCount = 0
    for (index in s.indices) {
        if (s[index] == 1) {
            studentCount++
        }

    }

    if (studentCount % k != 0) {
        return -1
    }

    // the leading zero should be ignored
    var index = 0
    while (index < s.size) {
        if (s[index] == 1) {
            break;
        }
        index++
    }

    // need to count the combinations between each room and multiple them together
    var oneCounts = 0
    var zeroCounts = 0
    var ways = 1
    while (index < s.size) {
        val bed = s[index++]
        if (bed == 0) {
            if (oneCounts == 0) {
                zeroCounts++
            }
        } else {
            oneCounts++
        }

        if (oneCounts == k) {
            val combinations = zeroCounts + 1 /* no zero would have 1 combination only */
            ways = (combinations * ways) % MOD
            zeroCounts = 0
            oneCounts = 0
        }
    }
    return ways
}

fun main(args: Array<String>) {
    val scan = Scanner(System.`in`)
    val k = scan.nextLine().toInt()

    val sInStrings = scan.nextLine().split(" ")
    val s = sInStrings.map { it.toInt() }.toIntArray()

    val result = findWays(k, s)

    println(result)
}
