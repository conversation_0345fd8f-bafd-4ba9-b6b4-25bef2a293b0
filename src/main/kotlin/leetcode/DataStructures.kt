package leetcode

import java.util.*

fun main() {
    printTree(toTreeNode(arrayOf(1,2,3,4,5,6,7)))
    printTree(toTreeNode(arrayOf(3,9,20,null,null,15,7)))

    printMatrix("[[1,2],[1,3],[1,4],[3,4],[4,5]]".toMatrix(), oneLine = true)
    printMatrix("[[1,2],[1,3],[1,4],[3,4],[4,5]]".toMatrix(), oneLine = false)
}

class TreeNode(var `val`: Int) {
    var left: TreeNode? = null
    var right: TreeNode? = null
}

class ListNode(val `val`: Int) {
    var next: ListNode? = null

    override fun toString(): String {
        return "$`val`"
    }
}

fun toTreeNode(levelOrderedList: Array<Int?>): TreeNode {
    val queue = LinkedList<TreeNode>()
    val root = TreeNode(levelOrderedList[0]!!)
    queue.add(root)
    var index = 1
    while (index <= levelOrderedList.lastIndex) {
        val node = queue.poll()
        if (levelOrderedList[index] != null) {
            node.left = TreeNode(levelOrderedList[index++]!!)
            queue.offer(node.left)
        } else {
            index++
        }
        if (index <= levelOrderedList.lastIndex && levelOrderedList[index] != null) {
            node.right = TreeNode(levelOrderedList[index++]!!)
            queue.offer(node.right)
        } else {
            index++
        }
    }
    return root
}

fun String.toListNode(): ListNode {
    val array = this.toIntArray()
    val head = ListNode(array[0])
    var node = head
    for (index in 1..array.lastIndex) {
        val newNode = ListNode(array[index])
        node.next = newNode
        node = node.next!!
    }
    return head
}

fun printListNode(node: ListNode?) {
    val sb = StringBuilder()
    sb.append('[')
    var current = node
    while (current != null) {
        sb.append(current.`val`)
        if (current.next != null) {
            sb.append(",")
        }
        current = current.next
    }
    sb.append(']')
    println(sb.toString())
}

fun printTree(node: TreeNode) {
    fun traverseNodes(
        sb: StringBuilder, padding: String?, pointer: String?, node: TreeNode?,
        hasRightSibling: Boolean
    ) {
        if (node != null) {
            sb.append("\n")
            sb.append(padding)
            sb.append(pointer)
            sb.append(node.`val`)

            val paddingBuilder = StringBuilder(padding)
            if (hasRightSibling) {
                paddingBuilder.append("│  ")
            } else {
                paddingBuilder.append("   ")
            }

            val paddingForBoth = paddingBuilder.toString()
            val pointerRight = "└─R─"
            val pointerLeft = if ((node.right != null)) "├─L─" else "└─L─"

            traverseNodes(sb, paddingForBoth, pointerLeft, node.left, node.right != null)
            traverseNodes(sb, paddingForBoth, pointerRight, node.right, false)
        }
    }
    val sb = StringBuilder()
    sb.append(node.`val`)

    val pointerRight = "└─R─"
    val pointerLeft = if ((node.right != null)) "├─L─" else "└─L─"

    traverseNodes(sb, "", pointerLeft, node.left, node.right != null)
    traverseNodes(sb, "", pointerRight, node.right, false)
    println(sb.toString())
}

fun String.toIntArray(): IntArray = this.replace("[", "")
    .replace("]", "")
    .split(",")
    .map { it.trim() }
    .map { it.toInt() }
    .toIntArray()

fun String.toMatrix(): Array<IntArray> {
    return this.replace("[[", "").replace("]]", "")
        .split("],[")
        .map { it.trim() }
        .map { line -> line.split(",").map { it.trim().toInt() }.toIntArray() }
        .toTypedArray()
}

fun String.toArrayOfCharArray(): Array<CharArray> {
    return this.replace("[[", "").replace("]]", "")
        .split("],[")
        .map { it.trim() }
        .map { line -> line.toCharArray() }
        .toTypedArray()
}

fun printMatrix(matrix: Array<IntArray>, oneLine: Boolean = true) {
    print("[")
    if (oneLine) println()
    matrix.forEachIndexed { rowIndex, row ->
        print("[")
        row.forEachIndexed { colIndex, col ->
            print(col)
            if (colIndex != row.lastIndex) print(",")
        }
        print("]")
        if (rowIndex != matrix.lastIndex) print(",")
        if (oneLine) println()
    }
    print("]")
    println()
}
