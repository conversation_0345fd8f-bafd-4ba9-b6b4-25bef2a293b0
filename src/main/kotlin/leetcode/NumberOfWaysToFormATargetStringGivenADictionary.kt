package leetcode

fun main() {
    // 6
    println(
        numWays(
            arrayOf("acca","bbbb","caca"),
            "aba"
        )
    )
    // 642306
    println(
        numWays(
            arrayOf("dcdccdabba","ddabcadabc","dbcdbaccba","cddadbdccd","adacdbaadb","cababaccbc","ccbccbaabd","bcdacdadcb","daddbbcaba","abbcaddbab","bbcdabadab","cabababccb","cadddddcba","accaabadbd","baabcabdbc","acbacdbbbc"),
            "dcdaad"
        )
    )
    // 677452090
    println(
        numWays(
            arrayOf("cbabddddbc","addbaacbbd","cccbacdccd","cdcaccacac","dddbacabbd","bdbdadbccb","ddadbacddd","bbccdddadd","dcabaccbbd","ddddcddadc","bdcaaaabdd","adacdcdcdd","cbaaadbdbb","bccbabcbab","accbdccadd","dcccaaddbc","cccccacabd","acacdbcbbc","dbbdbaccca","bdbddbddda","daabadbacb","baccdbaada","ccbabaabcb","dcaabccbbb","bcadddaacc","acddbbdccb","adbddbadab","dbbcdcbcdd","ddbabbadbb","bccbcbbbab","dabbbdbbcb","dacdabadbb","addcbbabab","bcbbccadda","abbcacadac","ccdadcaada","bcacdbccdb"),
            "bcbbcccc"
        )
    )
}

private fun numWays(words: Array<String>, target: String): Int {
    // time: O(wordLength*targetLength + wordLength*words.size)
    // space: O(wordLength*targetLength)
    val wordLength = words[0].length
    val targetLength = target.length

    val dp = Array(wordLength) { IntArray(targetLength) { -1 } }
    val charFrequency = Array(wordLength) { IntArray(26) }

    for (j in words.indices) {
        for (k in words[j].indices) {
            val charInInt = words[j][k] - 'a'
            charFrequency[k][charInInt]++
        }
    }

    return findWords(words, target, 0, 0, dp, charFrequency)
}

private fun findWords(
    words: Array<String>, target: String,
    i: Int, k: Int,
    dp: Array<IntArray>, charFrequency: Array<IntArray>
): Int {
    val MOD = 1000000007
    // matched
    if (i == target.length) return 1
    // no more column in the words or not enough column left to match
    if (k == words[0].length || words[0].length - k < target.length - i) return 0
    if (dp[k][i] != -1) return dp[k][i]

    var countWays = 0L
    val curPos = target[i] - 'a'

    // not match
    countWays += findWords(words, target, i, k + 1, dp, charFrequency)
    // match
    countWays += charFrequency[k][curPos] * findWords(words, target, i + 1, k + 1, dp, charFrequency).toLong()

    dp[k][i] = (countWays % MOD).toInt()
    return dp[k][i]
}