package leetcode

import java.util.*

// https://leetcode.com/problems/find-the-safest-path-in-a-grid/description/?envType=daily-question&envId=2024-05-15
fun main() {
    /*val grid = listOf(
        listOf(0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1),
        listOf(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1),
        listOf(1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1),
        listOf(1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1),
        listOf(1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0),
        listOf(1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0),
    )*/
    val grid = listOf(
        listOf(0,0,1),
        listOf(0,0,0),
        listOf(0,0,0),
    )
    println(maximumSafenessFactor(grid))
}

    private val directions = arrayOf(Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0))
    fun maximumSafenessFactor(grid: List<List<Int>>): Int {
        // time: O(n^2*log(n))
        // space: O(n^2)
        val n = grid.size
        if (grid[0][0] == 1 || grid[n - 1][n - 1] == 1) return 0

        // find all manhattan distances by using BFS
        val manhattanDistances = findManhattanDistances(grid)

        // find the path with maximum manhattan distance
        val maxHeap = PriorityQueue(Comparator { a: IntArray, b: IntArray -> b[2] - a[2] })
        maxHeap.offer(intArrayOf(0, 0 , manhattanDistances[0][0]))
        manhattanDistances[0][0] = -1 // mark as visited
        while (maxHeap.isNotEmpty()) {
            val item = maxHeap.poll()
            val rowIndex = item[0]
            val colIndex = item[1]
            val currentManhattanDistances = item[2]
            if (rowIndex == n - 1 && colIndex == n - 1) {
                return currentManhattanDistances
            }
            for (direction in directions) {
                val newRowIndex = rowIndex + direction.first
                val newColIndex = colIndex + direction.second
                if (isValidCell(newRowIndex, newColIndex, n) && manhattanDistances[newRowIndex][newColIndex] != -1) {
                    maxHeap.offer(
                        intArrayOf(
                            newRowIndex,
                            newColIndex,
                            minOf(currentManhattanDistances, manhattanDistances[newRowIndex][newColIndex]))
                    )
                    // mark the cell as visited
                    manhattanDistances[newRowIndex][newColIndex] = -1
                }
            }
        }
        return -1
    }

    private fun findManhattanDistances(
        grid: List<List<Int>>
    ): Array<IntArray> {
        val n = grid.size
        val manhattanDistances = Array(n) { IntArray(n) }
        val queue = LinkedList<Pair<Int, Int>>()
        for (i in 0 until n) {
            for (j in 0 until n) {
                if (grid[i][j] == 1) {
                    queue.offer(Pair(i, j))
                    manhattanDistances[i][j] = 0
                } else {
                    manhattanDistances[i][j] = -1
                }
            }
        }

        while(queue.isNotEmpty()) {
            val queueSize = queue.size
            var count = 0
            while (count < queueSize) {
                val node = queue.poll()
                val rowIndex = node.first
                val colIndex = node.second
                val currentManhattanDistance = manhattanDistances[rowIndex][colIndex]
                for (direction in directions) {
                    val newRowIndex = rowIndex + direction.first
                    val newColIndex = colIndex + direction.second
                    if (isValidCell(newRowIndex, newColIndex, n) && manhattanDistances[newRowIndex][newColIndex] == -1) {
                        manhattanDistances[newRowIndex][newColIndex] = 1 + currentManhattanDistance
                        queue.offer(Pair(newRowIndex, newColIndex))
                    }
                }
                count++
            }
        }
        return manhattanDistances
    }

    private fun isValidCell(rowIndex: Int, colIndex: Int, gridSize: Int): Boolean {
        return rowIndex in 0 until gridSize && colIndex in 0 until gridSize
    }
