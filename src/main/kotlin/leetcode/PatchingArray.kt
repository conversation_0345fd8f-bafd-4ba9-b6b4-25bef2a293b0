package leetcode

// https://leetcode.com/problems/patching-array/description/?envType=daily-question&envId=2024-06-16
fun main() {
    println(minPatches(intArrayOf(2, 3), 6))
    println(minPatches(intArrayOf(1, 5, 10), 20))
}

fun minPatches(nums: IntArray, n: Int): Int {
    // time: O(n)
    // space: (1)
    var missing = 1L
    var patches = 0
    var index = 0
    while (missing <= n) {
        if (index <= nums.lastIndex && nums[index] <= missing) {
            missing += nums[index]
            index++
        } else {
            missing += missing
            patches++
        }
    }
    return patches
}