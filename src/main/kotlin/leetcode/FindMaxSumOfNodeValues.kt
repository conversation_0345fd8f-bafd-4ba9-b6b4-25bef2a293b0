package leetcode

import java.util.*
import kotlin.math.max


// https://leetcode.com/problems/find-the-maximum-sum-of-node-values/description/?envType=daily-question&envId=2024-05-19

fun main() {
    // 6
    println(
        maximumValueSum(
            intArrayOf(1, 2, 1),
            3,
            arrayOf(
                intArrayOf(0, 1),
                intArrayOf(0, 2),
            )
        )
    )
    // 9
    println(
        maximumValueSum(
            intArrayOf(2, 3),
            7,
            arrayOf(
                intArrayOf(0, 1),
            )
        )
    )
    // 42
    println(
        maximumValueSum(
            intArrayOf(7, 7, 7, 7, 7, 7),
            3,
            arrayOf(
                intArrayOf(0, 1),
                intArrayOf(0, 2),
                intArrayOf(0, 3),
                intArrayOf(0, 4),
                intArrayOf(0, 5),
            )
        )
    )
    // 260
    println(
        maximumValueSum(
            // 24 -> 30, 78 -> 72, 1 -> 7, 97 -> 103, 44 -> 42
            intArrayOf(24, 78, 1, 97, 44),
            6,
            arrayOf(
                intArrayOf(0, 2),
                intArrayOf(1, 2),
                intArrayOf(4, 2),
                intArrayOf(3, 4),
            )
        )
    )
    // 507
    println(
        maximumValueSum(
            intArrayOf(78, 43, 92, 97, 95, 94),
            6,
            arrayOf(
                intArrayOf(1, 2),
                intArrayOf(3, 0),
                intArrayOf(4, 0),
                intArrayOf(0, 1),
                intArrayOf(1, 5),
            )
        )
    )
    // 407
    println(
        maximumValueSum(
            intArrayOf(67, 13, 79, 13, 75, 11, 0, 41, 94),
            7,
            arrayOf(
                intArrayOf(0, 1),
                intArrayOf(3, 7),
                intArrayOf(4, 7),
                intArrayOf(6, 5),
                intArrayOf(6, 0),
                intArrayOf(0, 2),
                intArrayOf(7, 2),
                intArrayOf(7, 8),
            )
        )
    )
}

fun maximumValueSum(nums: IntArray, k: Int, edges: Array<IntArray?>?): Long {
    val memo = Array(nums.size) { LongArray(2) { -1L } }
    return maxSumOfNodes(0, 1, nums, k, memo)
}

private fun maxSumOfNodes(
    index: Int, isEven: Int, nums: IntArray, k: Int,
    memo: Array<LongArray>
): Long {
    if (index == nums.size) {
        // If the operation is performed on an odd number of elements return
        // INT_MIN
        return if (isEven == 1) 0 else Int.MIN_VALUE.toLong()
    }
    if (memo[index][isEven] != -1L) {
        return memo[index][isEven]
    }
    // No operation performed on the element
    val noXorDone = nums[index] + maxSumOfNodes(index + 1, isEven, nums, k, memo)
    // XOR operation is performed on the element
    val xorDone = (nums[index] xor k) +
            maxSumOfNodes(index + 1, isEven xor 1, nums, k, memo)

    // Memoize and return the result
    return maxOf(xorDone, noXorDone).also { memo[index][isEven] = it }.toLong()
}