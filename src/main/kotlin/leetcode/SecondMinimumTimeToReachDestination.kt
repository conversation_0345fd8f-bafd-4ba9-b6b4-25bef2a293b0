package leetcode

import java.util.*

/**
 * https://leetcode.com/problems/second-minimum-time-to-reach-destination/description/
 */
fun main() {
    // 5
    println(secondMinimum(
        n = 2,
        edges = "[[1,2]]".toMatrix(),
        time = 1,
        change = 2
    ))
    // 13
    println(secondMinimum(
        n = 5,
        edges = "[[1,2],[1,3],[1,4],[3,4],[4,5]]".toMatrix(),
        time = 3,
        change = 5
    ))
    // 11
    println(secondMinimum(
        n = 2,
        edges = "[[1,2]]".toMatrix(),
        time = 3,
        change = 2
    ))
    // 1700
    println(secondMinimum(
        n = 19,
        edges = "[[1,2],[2,3],[1,4],[2,5],[2,6],[2,7],[7,8],[8,9],[7,10],[9,11],[11,12],[1,13],[3,14],[13,15],[14,16],[8,17],[4,18],[11,19],[17,11],[3,19],[19,7],[12,5],[8,1],[15,7],[19,6],[18,9],[6,8],[14,19],[13,18],[15,2],[13,12],[1,5],[16,18],[3,16],[6,1],[18,14],[12,1],[16,6],[13,11],[1,14],[16,13],[11,16],[4,15],[17,5],[5,9],[12,2],[4,10],[9,16],[17,9],[3,5],[10,2],[18,1],[15,18],[12,17],[10,6],[10,18],[19,12],[12,15],[19,13],[1,19],[9,14],[4,3],[17,13],[9,3],[17,10],[19,10],[5,4],[5,7],[14,17],[1,10],[4,11],[6,4],[5,10],[7,14],[8,14],[18,17],[15,10],[11,8],[14,11],[7,3],[5,18],[13,8],[4,12],[11,3],[5,15],[15,9],[8,10],[13,3],[17,1],[10,11],[15,11],[19,2],[1,3],[7,4],[18,11],[2,14],[9,1],[17,15],[7,13],[12,16],[12,8],[6,12],[9,6],[2,17],[15,6],[16,2],[12,7],[7,9],[8,4]]".toMatrix(),
        time = 850,
        change = 411
    ))
}

private fun secondMinimum(n: Int, edges: Array<IntArray>, time: Int, change: Int): Int {
    // time: O(n + e)
    // space: O(n + e)

    val adj = mutableMapOf<Int, MutableList<Int>>()
    for ((a, b) in edges) {
        adj.computeIfAbsent(a) { mutableListOf() }.add(b)
        adj.computeIfAbsent(b) { mutableListOf() }.add(a)
    }
    val dist1 = IntArray(n + 1)
    val dist2 = IntArray(n + 1)
    for (i in 0..n) {
        dist1[i] = -1
        dist2[i] = -1
    }
    val queue = LinkedList<IntArray>()
    // start with node 1
    queue.offer(intArrayOf(1, 1))
    dist1[1] = 0
    while (queue.isNotEmpty()) {
        val (node, freq) = queue.poll()
        var timeTaken = if (freq == 1) dist1[node] else dist2[node]

        if ((timeTaken / change) % 2 == 1) {
            timeTaken = change * (timeTaken / change + 1) + time
        } else {
            timeTaken += time
        }

        for (neighbor in adj[node]!!) {
            if (dist1[neighbor] == -1) {
                dist1[neighbor] = timeTaken
                queue.offer(intArrayOf(neighbor, 1))
            } else if(dist2[neighbor] == -1 && dist1[neighbor] != timeTaken) {
                if (neighbor == n) return timeTaken
                dist2[neighbor] = timeTaken
                queue.offer(intArrayOf(neighbor, 2))
            }
        }
    }
    return 0
}