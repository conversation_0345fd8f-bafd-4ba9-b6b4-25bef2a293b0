package sort;

import java.util.Arrays;

/**
 * <a href="https://www.geeksforgeeks.org/insertion-sort/">Insertion Sort</a>
 */
public class InsertionSort {
    public static void main(String[] args) {
        System.out.println(Arrays.toString(sort(new int[]{0})));
        System.out.println(Arrays.toString(sort(new int[]{1, 0})));
        System.out.println(Arrays.toString(sort(new int[]{12, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{12, 17, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11, 3, 3, 3})));
    }

    private static int[] sort(int[] sourceArray) {
        for (int i = 1; i < sourceArray.length; i++) {
            int k = i;
            for (int j = i - 1; j >= 0; j--) {
                if (sourceArray[k] < sourceArray[j]) {
                    swap(sourceArray, k, j);
                    k = j;
                }
            }
        }
        return sourceArray;
    }

    private static void swap(int[] sourceArray, int i, int j) {
        int temp = sourceArray[i];
        sourceArray[i] = sourceArray[j];
        sourceArray[j] = temp;
    }
}
