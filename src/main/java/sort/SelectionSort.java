package sort;

import java.util.Arrays;

/**
 * <a href="https://www.geeksforgeeks.org/selection-sort">Selection Sort</a>
 */
public class SelectionSort {
    public static void main(String[] args) {
        System.out.println(Arrays.toString(sort(new int[]{0})));
        System.out.println(Arrays.toString(sort(new int[]{1, 0})));
        System.out.println(Arrays.toString(sort(new int[]{12, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{12, 17, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11, 3, 3})));
    }

    private static int[] sort(int[] sourceArray) {
        for (int i = 0; i < sourceArray.length; i++) {
            int smallestValueIndex = findSmallestValueIndex(sourceArray, i);
            swap(sourceArray, i, smallestValueIndex);
        }
        return sourceArray;
    }

    private static int findSmallestValueIndex(int[] sourceArray, int startIndex) {
        int smallestValue = sourceArray[startIndex];
        int smallestValueIndex = startIndex;
        for (int i = startIndex; i < sourceArray.length; i++) {
            if (sourceArray[i] < smallestValue) {
                smallestValue = sourceArray[i];
                smallestValueIndex = i;
            }
        }
        return smallestValueIndex;
    }

    private static void swap(int[] sourceArray, int i, int j) {
        int temp = sourceArray[i];
        sourceArray[i] = sourceArray[j];
        sourceArray[j] = temp;
    }
}
