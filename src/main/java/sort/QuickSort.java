package sort;

import java.util.Arrays;

/**
 * <a href="https://www.geeksforgeeks.org/quick-sort">Quick Sort</a>
 */
public class QuickSort {
    public static void main(String[] args) {
        System.out.println(Arrays.toString(sort(new int[]{10, 80, 30, 90, 40, 50, 70})));
        System.out.println(Arrays.toString(sort(new int[]{0})));
        System.out.println(Arrays.toString(sort(new int[]{1, 0})));
        System.out.println(Arrays.toString(sort(new int[]{12, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{12, 17, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11, 3, 3})));
    }

    private static int[] sort(int[] sourceArray) {
        sort(sourceArray, 0, sourceArray.length - 1);
        return sourceArray;
    }

    private static void sort(int[] sourceArray, int startIndex, int endIndex) {
        if (startIndex < endIndex) {
            int pivotIndex = findPivotIndex(sourceArray, startIndex, endIndex);
            sort(sourceArray, startIndex, pivotIndex - 1);
            sort(sourceArray, pivotIndex + 1, endIndex);
        }
    }

    /**
     * This method would find out the pivot index and also put it in the correct position
     */
    private static int findPivotIndex(int[] sourceArray, int startIndex, int endIndex) {
        int pivot = sourceArray[endIndex];
        int lastSmallerValueIndex = startIndex - 1;
        for (int i = startIndex; i < endIndex; i++) {
            if (sourceArray[i] < pivot) {
                lastSmallerValueIndex++;
                swap(sourceArray, lastSmallerValueIndex, i);
            }
        }
        final int pivotIndex = lastSmallerValueIndex + 1;
        swap(sourceArray, pivotIndex, endIndex);
        return pivotIndex;
    }

    private static void swap(int[] sourceArray, int i, int j) {
        int temp = sourceArray[i];
        sourceArray[i] = sourceArray[j];
        sourceArray[j] = temp;
    }
}
