package sort;

import java.util.Arrays;

/**
 * <a href="https://www.geeksforgeeks.org/merge-sort/">Merge Sort</a>
 */
public class MergeSort {
    public static void main(String[] args) {
        System.out.println(Arrays.toString(sort(new int[]{0})));
        System.out.println(Arrays.toString(sort(new int[]{1, 0})));
        System.out.println(Arrays.toString(sort(new int[]{12, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{12, 17, 11, 13, 5, 6})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11})));
        System.out.println(Arrays.toString(sort(new int[]{3, 1, 2, 2, 7, 5, 12, 11, 3, 3})));
    }

    private static int[] sort(int[] sourceArray) {
        sort(sourceArray, 0, sourceArray.length - 1);
        return sourceArray;
    }

    private static void sort(int[] sourceArray, int startIndex, int endIndex) {
        if (startIndex < endIndex) {
            int middleIndex = ((endIndex - startIndex) / 2) + startIndex;
            sort(sourceArray, startIndex, middleIndex);
            sort(sourceArray, middleIndex + 1, endIndex);
            merge(sourceArray, startIndex, middleIndex, endIndex);
        }
    }

    private static void merge(int[] sourceArray, int startIndex, int middleIndex, int endIndex) {
        int[] leftArray = new int[middleIndex - startIndex + 1];
        for (int i = 0; i < leftArray.length; i++) {
            leftArray[i] = sourceArray[startIndex + i];
        }
        int[] rightArray = new int[endIndex - middleIndex];
        for (int i = 0; i < rightArray.length; i++) {
            rightArray[i] = sourceArray[middleIndex + 1 + i];
        }

        int leftArrayIndex = 0;
        int rightArrayIndex = 0;
        int resultArrayIndex = startIndex;
        while (leftArrayIndex < leftArray.length && rightArrayIndex < rightArray.length) {
            if (leftArray[leftArrayIndex] <= rightArray[rightArrayIndex]) {
                sourceArray[resultArrayIndex] = leftArray[leftArrayIndex];
                leftArrayIndex++;
            } else {
                sourceArray[resultArrayIndex] = rightArray[rightArrayIndex];
                rightArrayIndex++;
            }
            resultArrayIndex++;
        }

        while (leftArrayIndex < leftArray.length) {
            sourceArray[resultArrayIndex] = leftArray[leftArrayIndex];
            leftArrayIndex++;
            resultArrayIndex++;
        }

        while (rightArrayIndex < rightArray.length) {
            sourceArray[resultArrayIndex] = rightArray[rightArrayIndex];
            rightArrayIndex++;
            resultArrayIndex++;
        }
    }
}
