package geeksforgeeks;

/**
 * https://www.geeksforgeeks.org/problems/distribute-candies-in-a-binary-tree/1
 * <p>
 * https://www.geeksforgeeks.org/distribute-candies-in-a-binary-tree/
 */
public class DistributedCandy {

    public static void main(String[] args) {
        Node root = new Node(4);
        root.left = new Node(0);
        root.right = new Node(0);
        root.left.left = new Node(0);
        root.left.right = new Node(0);
        root.right.left = new Node(0);
        root.right.right = new Node(0);

        System.out.println(distributeCandy(root));
    }

    static int steps = 0;

    public static int distributeCandy(Node root) {
        steps = 0;
        countMoves(root);
        return steps;
    }

    static int countMoves(Node node) {
        if (node == null) return 0;

        int leftCount = countMoves(node.left);
        int rightCount = countMoves(node.right);

        steps += Math.abs(leftCount) + Math.abs(rightCount);
        final int movesForBalanceThisNode = leftCount + rightCount + node.data - 1;
        System.out.printf("node[%s] - leftRequiredMoves: %s, rightRequiredMoves: %s, movesForBalanceThisNode: %s\n",
                node.id, leftCount, rightCount, movesForBalanceThisNode);
        return movesForBalanceThisNode;
    }

    private static int NODE_ID = 1;

    private static class Node {
        int id;
        int data;
        Node left;
        Node right;

        Node(int data) {
            this(NODE_ID++, data);
        }

        Node(int id, int data) {
            this.id = id;
            this.data = data;
            left = null;
            right = null;
        }
    }
}
