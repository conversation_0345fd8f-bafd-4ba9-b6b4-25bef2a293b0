package geeksforgeeks;

import ds.IntNode;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.Queue;

/**
 * <a href="https://www.geeksforgeeks.org/averages-levels-binary-tree/">Average levels of tree</a>
 */
public class AverageLevelsOfTree {
    public static void main(String[] args) {
        IntNode root = new IntNode(4);
        root.left = new IntNode(2);
        root.left.left = new IntNode(3);
        root.left.right = new IntNode(5);
        root.right = new IntNode(9);
        root.right.right = new IntNode(7);
        root.right.right.left = new IntNode(10);

        final double[] result = averageOfLevels(root);
        // 4 5.5 5 10
        System.out.println(Arrays.toString(result));
    }

    public static double[] averageOfLevels(IntNode root) {
        if (root == null) {
            return new double[0];
        }

        int maxHeight = findMaxHeight(root);
        double[] result = new double[maxHeight];
        Queue<IntNode> queue = new LinkedList<>();
        queue.offer(root);
        int height = 0;
        while (!queue.isEmpty()) {
            double sum = 0.0;
            int count = 0;
            int size = queue.size();
            for (int i = 0; i < size; i++) {
                IntNode node = queue.poll();
                sum += node.value;
                count++;
                if (node.left != null) {
                    queue.offer(node.left);
                }
                if (node.right != null) {
                    queue.offer(node.right);
                }
            }
            double average = sum / count;
            result[height++] = average;
        }
        return result;
    }

    private static int findMaxHeight(IntNode root) {
        if (root == null) {
            return 0;
        } else {
            return 1 + Math.max(findMaxHeight(root.left), findMaxHeight(root.right));
        }
    }
}
