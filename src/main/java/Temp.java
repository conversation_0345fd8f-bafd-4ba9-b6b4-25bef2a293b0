import java.util.HashMap;
import java.util.Map;

public class Temp {
    static final int MOD = 1000000007;

    public static void main(String[] args) {
        minGroupsForValidAssignment(new int[] {10,10,10,3,1,1});
    }

    public static int minGroupsForValidAssignment(int[] nums) {
        Map<Integer, Integer> ball2Counts = new HashMap<>();
        for (int x : nums) {
            ball2Counts.merge(x, 1, Integer::sum);
        }
        int minCount = nums.length;
        for (int count : ball2Counts.values()) {
            minCount = Math.min(minCount, count);
        }
        for (;; --minCount) {
            int ans = 0;
            for (int count : ball2Counts.values()) {
                if (count / minCount < count % minCount) {
                    ans = 0;
                    break;
                }
                ans += (count + minCount) / (minCount + 1);
            }
            if (ans > 0) {
                return ans;
            }
        }
    }
}
