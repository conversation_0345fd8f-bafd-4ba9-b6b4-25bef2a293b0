package hackerrank.practice.interview_preparation_kit.sorting.comparator;

import java.util.*;

class Player {
    String name;
    int score;

    Player(String name, int score) {
        this.name = name;
        this.score = score;
    }
}

class Checker implements Comparator<Player> {
    private static final Comparator<Player> comparator = Comparator
            .comparingInt((Player player) -> player.score).reversed()
            .thenComparing((Player player) -> player.name);
    /**
     * 1. decreasing score
     * 2. alphabetically ascending by name
     */
    public int compare(Player a, Player b) {
        return comparator.compare(a, b);
    }
}


public class Solution {

    public static void main(String[] args) {
        Scanner scan = new Scanner(System.in);
        int n = scan.nextInt();

        Player[] player = new Player[n];
        Checker checker = new Checker();

        for(int i = 0; i < n; i++){
            player[i] = new Player(scan.next(), scan.nextInt());
        }
        scan.close();

        Arrays.sort(player, checker);
        for(int i = 0; i < player.length; i++){
            System.out.printf("%s %s\n", player[i].name, player[i].score);
        }
    }
}
