package other.sam;

import java.util.ArrayList;
import java.util.List;

public class CombinationSumFinder {

    public static void main(String[] args) {
        int[] inputArray = {
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                21, 22, 23, 24, 25, 26, 27, 28, 29, 30
        };
        int sum = 0;

        List<List<Integer>> result = findCombinationSums(inputArray);
        for (List<Integer> combination : result) {
            int combinationSum = combination.stream().mapToInt(Integer::intValue).sum();
            sum += combinationSum;
        }
        System.out.println("The sum of all possible unique combinations: " + sum);
    }

    public static List<List<Integer>> findCombinationSums(int[] inputArray) {
        List<List<Integer>> result = new ArrayList<>();
        findCombinationSums(inputArray, 0, new ArrayList<>(), result);
        return result;
    }

    private static void findCombinationSums(int[] inputArray, int start, List<Integer> current, List<List<Integer>> result) {
        if (current.size() > 0) {
            result.add(new ArrayList<>(current));
        }
        for (int i = start; i < inputArray.length; i++) {
            current.add(inputArray[i]);
            findCombinationSums(inputArray, i+1, current, result);
            current.remove(current.size() - 1);
        }
    }
}
