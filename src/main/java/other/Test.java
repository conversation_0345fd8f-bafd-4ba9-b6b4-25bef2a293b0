package other;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class Test {
    public static void main(String[] args) {
        List<Integer> input = Arrays.asList(1, 2, 3, 4, 5);
        List<List<Integer>> result = allCombinations(input);
        result.forEach(System.out::println);
    }

    public static List<List<Integer>> allCombinations(List<Integer> input) {
        List<List<Integer>> result = new ArrayList<>();
        for (int size = 1; size <= input.size(); size++) {
            result.addAll(combinations(input, size));
        }
        return result;
    }

    public static List<List<Integer>> combinations(List<Integer> input, int size) {
        if (size == 1) {
            return input.stream()
                    .map(Arrays::asList)
                    .collect(Collectors.toList());
        } else {
            return IntStream.range(0, input.size())
                    .boxed()
                    .flatMap(i -> combinations(input.subList(i + 1, input.size()), size - 1).stream()
                            .map(l -> {
                                List<Integer> r = new ArrayList<>();
                                r.add(input.get(i));
                                r.addAll(l);
                                return r;
                            }))
                    .collect(Collectors.toList());
        }
    }
}
