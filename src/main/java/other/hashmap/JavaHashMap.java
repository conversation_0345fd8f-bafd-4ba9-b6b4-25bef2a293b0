package other.hashmap;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * https://leetcode.com/problems/design-hashmap/
 */
public class JavaHashMap {
    private static final int SIZE = 25000;
    private final Entry[] buckets;

    private static String readFromInputStream(InputStream inputStream)
            throws IOException {
        StringBuilder resultStringBuilder = new StringBuilder();
        try (BufferedReader br
                     = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = br.readLine()) != null) {
                resultStringBuilder.append(line).append("\n");
            }
        }
        return resultStringBuilder.toString();
    }

    public static void main(String[] args) throws IOException {
        /*String input = "[\"MyHashMap\",\"put\",\"put\",\"get\",\"get\",\"put\",\"get\", \"remove\", \"get\",\"put\",\"get\"]\n" +*/
        /*        "[[],[1,1],[2,2],[1],[3],[2,1],[2],[2],[2],[47498,69263],[47498]]";*/
        InputStream inputStream = JavaHashMap.class.getResourceAsStream("/other/hashmap/input.txt");
        String input = readFromInputStream(inputStream);
        String[] lines = input.split("\n");
        String[] commands = lines[0].substring(1, lines[0].length() - 1).split(",");
        String[] data = lines[1].replaceAll("],\\[", "]\n\\[").substring(1, lines[1].length() - 1).split("\n");

        JavaHashMap map = new JavaHashMap();
        Integer[] result = new Integer[commands.length];
        for (int index = 0; index < commands.length; index++) {
            String command = commands[index].trim().replace("\"", "");
            String d = data[index].trim().replace("[", "").replace("]", "");
            switch (command) {
                case "MyHashMap":
                    result[index] = null;
                    break;
                case "put":
                    String[] dd = d.split(",");
                    int key = Integer.parseInt(dd[0].trim());
                    int value = Integer.parseInt(dd[1].trim());
                    System.out.printf("put %s as %s\n", key, value);
                    map.put(key, value);
                    result[index] = null;
                    break;
                case "get":
                    System.out.printf("get %s\n", Integer.parseInt(d));
                    result[index] = map.get(Integer.parseInt(d));
                    break;
                case "remove":
                    System.out.printf("remove %s\n", Integer.parseInt(d));
                    map.remove(Integer.parseInt(d));
                    result[index] = null;
                    break;
            }
        }

        String output = Arrays.stream(result)
                .map(String::valueOf)
                .collect(Collectors.joining(",", "[", "]"));
        System.out.println(output);

        InputStream outputStream = JavaHashMap.class.getResourceAsStream("/other/hashmap/output.txt");
        String expectedOutput = readFromInputStream(outputStream);
        System.out.println(expectedOutput);

        String[] e = expectedOutput.replace("[", "").replace("]", "").split(",");
        for (int index = 0; index < e.length; index++) {
            Integer expected;
            String value = e[index].trim();
            if (value.equals("null")) {
                expected = null;
            } else {
                expected = Integer.parseInt(value);
            }
            Integer actual = result[index];
            if (!Objects.equals(actual, expected)) {
                System.out.printf("index: %s, expected: %s, actual: %s\n", index, expected, actual);
            }
        }
    }

    /**
     * Initialize your data structure here.
     */
    public JavaHashMap() {
        // buckets = (Entry<K, V>[]) Array.newInstance(Entry.class, SIZE);
        buckets = new Entry[SIZE];
    }

    /**
     * value will always be non-negative.
     */
    public void put(int key, int value) {
        int bucketIndex = findBucketIndex(key);
        Entry entry = buckets[bucketIndex];
        if (entry == null) {
            buckets[bucketIndex] = new Entry(key, value);
        } else {
            Entry lastEntry = entry;
            while (true) {
                if (lastEntry.key == key) {
                    lastEntry.value = value;
                    break;
                }
                if (lastEntry.next == null) {
                    lastEntry.next = new Entry(key, value);
                    break;
                } else {
                    lastEntry = lastEntry.next;
                }
            }
        }
    }

    /**
     * Returns the value to which the specified key is mapped, or -1 if this map contains no mapping for the key
     */
    public int get(int key) {
        int bucketIndex = findBucketIndex(key);
        Entry lastEntry = buckets[bucketIndex];
        while (lastEntry != null) {
            if (lastEntry.key == key) {
                return lastEntry.value;
            }
            lastEntry = lastEntry.next;
        }
        return -1;
    }

    /**
     * Removes the mapping of the specified value key if this map contains a mapping for the key
     */
    public void remove(int key) {
        int bucketIndex = findBucketIndex(key);
        Entry entry = buckets[bucketIndex];
        Entry lastEntry = entry;
        Entry previousEntry = null;
        while (lastEntry != null) {
            if (lastEntry.key == key) {
                if (previousEntry == null) {
                    if (lastEntry.next == null) {
                        buckets[bucketIndex] = null;
                    } else {
                        buckets[bucketIndex] = lastEntry.next;
                    }
                } else {
                    if (lastEntry == entry) {
                        buckets[bucketIndex] = lastEntry.next;
                    } else {
                        previousEntry.next = lastEntry.next;
                    }
                }
                break;
            }
            previousEntry = lastEntry;
            lastEntry = lastEntry.next;
        }
    }

    private int findBucketIndex(int key) {
        return key & SIZE - 1;
    }

    private static class Entry {
        int key;
        int value;
        Entry next;

        Entry(int key, int value) {
            this.key = key;
            this.value = value;
        }
    }
}
