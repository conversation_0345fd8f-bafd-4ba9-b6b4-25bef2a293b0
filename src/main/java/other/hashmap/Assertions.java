package other.hashmap;

import java.util.Arrays;

public class Assertions {
    public static void assertIntArrayEquals(final int[] actual, final int[] expected) {
        for (int i = 0; i < actual.length; i++) {
            if (actual[i] != expected[i]) {
                throw new AssertionError(
                        String.format(
                                "actual value is \"%s\" but expected value is \"%s\"\n",
                                Arrays.toString(actual), Arrays.toString(expected)
                        )
                );
            }
        }
    }
}
