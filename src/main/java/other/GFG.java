package other;

import java.util.Collections;
import java.util.Vector;

// https://www.geeksforgeeks.org/minimize-count-of-divisions-by-d-to-obtain-at-least-k-equal-array-elements/
public class GFG {
    // Function to return minimum
// number of moves required
    @SuppressWarnings("unchecked")
    static int getMinimumMoves(int arraySize, int threshold,
                               int d, int[] a) {
        int MAX = 100000;

        // Stores the number of moves
        // required to obtain respective
        // values from the given array
        Vector<Integer>[] v = new Vector[MAX];
        for (int i = 0; i < v.length; i++)
            v[i] = new Vector<>();

        // Traverse the array
        for (int i = 0; i < arraySize; i++) {
            int cnt = 0;

            // Insert 0 into V[a[i]] as
            // it is the initial state
            v[a[i]].add(0);

            while (a[i] > 0) {
                a[i] /= d;
                cnt++;

                // Insert the moves required
                // to obtain current a[i]
                v[a[i]].add(cnt);
            }
        }

        int ans = Integer.MAX_VALUE;

        // Traverse v[] to obtain
        // minimum count of moves
        for (int i = 0; i < MAX; i++) {

            // Check if there are at least
            // K equal elements for v[i]
            if (v[i].size() >= threshold) {
                int move = 0;

                Collections.sort(v[i]);

                // Add the sum of minimum K moves
                for (int numberOfElementsInSameValue = 0; numberOfElementsInSameValue < threshold; numberOfElementsInSameValue++) {
                    Integer nm = v[i].get(numberOfElementsInSameValue);
                    move += nm;
                }

                // Update answer
                ans = Math.min(ans, move);
            }
        }

        // Return the final answer
        return ans;
    }

    // Driver Code
    public static void main(String[] args) {
        int N = 5, K = 3, D = 2;
        int[] A = {1, 2, 3, 4, 5};

        System.out.print(getMinimumMoves(N, K, D, A));
    }
}
