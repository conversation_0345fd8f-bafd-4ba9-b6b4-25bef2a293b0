package other;

import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.groupingBy;

public class SamQuestion {
    public static void main(String[] args) {
        List<Integer> A = Arrays.asList(1, 2, 3, 4, 5, 1);
        List<Integer> B = Arrays.asList(6, 7);

        List<List<Integer>> combinationsOfA = allCombinations(A);
//        System.out.println(combinationsOfA);
        Map<Integer, List<List<Integer>>> sum2CombinationsA = sum2Combinations(combinationsOfA);
//        System.out.println(sum2CombinationsA);

        List<List<Integer>> combinationsOfB = allCombinations(B);
//        System.out.println(combinationsOfB);
        Map<Integer, List<List<Integer>>> sum2CombinationsB = sum2Combinations(combinationsOfB);
//        System.out.println(sum2CombinationsB);

        Map<Integer, List<List<Integer>>> matched = new HashMap<>();
        // we shall iterate the map with smaller size
        sum2CombinationsB.forEach((sum, combinationsB) -> {
            if (sum2CombinationsA.containsKey(sum)) {
                List<List<Integer>> matchedLists;
                if (matched.containsKey(sum)) {
                    matchedLists = matched.get(sum);
                } else {
                    matchedLists = new ArrayList<>();
                }
                matchedLists.addAll(sum2CombinationsA.get(sum));
                matchedLists.addAll(combinationsB);
                matched.put(sum, matchedLists);
            }
        });

        System.out.println(matched);
    }

    private static List<List<Integer>> allCombinations(List<Integer> input) {
        List<List<Integer>> result = new ArrayList<>();
        for (int size = 1; size <= input.size(); size++) {
            result.addAll(combinations(input, size));
        }
        return result;
    }

    private static List<List<Integer>> combinations(List<Integer> input, int size) {
        if (size == 1) {
            return input.stream()
                    .map(Arrays::asList)
                    .collect(Collectors.toList());
        } else {
            return IntStream.range(0, input.size())
                    .boxed()
                    .flatMap(i -> combinations(input.subList(i + 1, input.size()), size - 1).stream()
                            .map(l -> {
                                List<Integer> r = new ArrayList<>();
                                r.add(input.get(i));
                                r.addAll(l);
                                return r;
                            }))
                    .collect(Collectors.toList());
        }
    }

    private static Map<Integer, List<List<Integer>>> sum2Combinations(List<List<Integer>> combinationsOfB) {
        return combinationsOfB.stream()
                .collect(groupingBy(l -> l.stream().reduce(0, Integer::sum)));
    }

    public static List<List<Integer>> findCombinationSumsByAGivenNumber(List<Integer> intList, int givenNumber) {
        List<List<Integer>> result = new ArrayList<>();
        findCombinationSumsByAGivenNumber(intList, 0, new ArrayList<>(), result, givenNumber);
        return result;
    }

    private static void findCombinationSumsByAGivenNumber(List<Integer> intList, int start, List<Integer> current, List<List<Integer>> result, int givenNumber) {
        int currentSum = current.stream().mapToInt(Integer::intValue).sum();
        if (currentSum == givenNumber) {
            result.add(new ArrayList<>(current));
            return;
        }
        if (currentSum > givenNumber) {
            return;
        }
        for (int i = start; i < intList.size(); i++) {
            current.add(intList.get(i));
            findCombinationSumsByAGivenNumber(intList, i + 1, current, result, givenNumber);
            current.remove(current.size() - 1);
        }
    }
}
