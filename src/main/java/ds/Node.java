package ds;

import java.util.StringJoiner;

public class Node<V, N extends Node<V, N>> {

    private static int DEFAULT_ID = 1;

    public String id;
    public V value;
    public N left;
    public N right;

    public Node(V value) {
        this.id = Integer.toString(DEFAULT_ID++);
        this.value = value;
    }

    public Node(String id, V value) {
        this.id = id;
        this.value = value;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", Node.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("value=" + value)
                .add("left=" + left)
                .add("right=" + right)
                .toString();
    }
}
