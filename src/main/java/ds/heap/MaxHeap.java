package ds.heap;

public class MaxHeap {
    private final int maxSize;
    private final int[] array;
    private int size;

    public static void main(String[] args) {
        final MaxHeap heap = new MaxHeap(10);
        System.out.printf("initial - heap size: %d, max: %d\n", heap.size(), heap.getMax());
        heap.insert(3);
        heap.insert(10);
        heap.insert(12);
        heap.insert(8);
        heap.insert(2);
        heap.insert(14);
        System.out.printf("after insert - heap size: %d, max: %d\n", heap.size(), heap.getMax());

        for (int i = 0; i < 6; i++) {
            final int max = heap.removeMax();
            System.out.printf("after remove max value - heap size: %d, removed max: %d, current max: %d\n",
                    heap.size(), max, heap.getMax());
        }
    }

    public MaxHeap(int maxSize) {
        this.maxSize = maxSize;
        this.array = new int[maxSize];
    }

    public void insert(int value) {
        if (size == maxSize) {
            throw new IllegalStateException("the heap is already full");
        }
        size++;

        // put the new value at the end most position first
        int currentIndex = size - 1;
        array[currentIndex] = value;

        // move the new value into correct position
        while (currentIndex != 0 && array[parentIndex(currentIndex)] < array[currentIndex]) {
            swap(currentIndex, parentIndex(currentIndex));
            currentIndex = parentIndex(currentIndex);
        }
    }

    public int removeMax() {
        if (size == 0) {
            throw new IllegalStateException("the heap is empty");
        }
        if (size == 1) {
            size--;
            return array[0];
        }

        int max = array[0];
        array[0] = array[size - 1];
        size--;
        heapify(0);
        return max;
    }

    public int size() {
        return size;
    }

    public int getMax() {
        if (size == 0) {
            return Integer.MIN_VALUE;
        }
        return array[0];
    }

    private void heapify(final int currentIndex) {
        int leftIndex = leftChildIndex(currentIndex);
        int rightIndex = rightChildIndex(currentIndex);
        int largestIndex = currentIndex;
        if (leftIndex < size && array[leftIndex] > array[currentIndex]) {
            largestIndex = leftIndex;
        }
        if (rightIndex < size && array[rightIndex] > array[largestIndex]) {
            largestIndex = rightIndex;
        }
        if (largestIndex != currentIndex) {
            swap(currentIndex, largestIndex);
            heapify(largestIndex);
        }
    }

    private int parentIndex(int currentIndex) {
        return (currentIndex - 1) / 2;
    }

    private int leftChildIndex(int currentIndex) {
        return (2 * currentIndex) + 1;
    }

    private int rightChildIndex(int currentIndex) {
        return (2 * currentIndex) + 2;
    }

    private void swap(int i, int j) {
        int tmp = array[i];
        array[i] = array[j];
        array[j] = tmp;
    }
}
