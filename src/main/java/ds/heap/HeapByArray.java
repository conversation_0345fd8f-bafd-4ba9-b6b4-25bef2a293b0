package ds.heap;

import java.util.Arrays;

public class HeapByArray {
    public static void main(String[] args) {
        final int[] array = new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9 ,10};
        System.out.printf("initial: %s\n", Arrays.toString(array));
        convertToHeap(array);
        System.out.printf("after heapify: %s\n", Arrays.toString(array));
        for (int i = 0; i < array.length; i++) {
            final int max = removeMax(array, array.length - i);
            System.out.printf("after remove max value[%d]: %s\n", max, Arrays.toString(array));
        }
    }

    private static void convertToHeap(int[] array) {
        int lastNonLeafNodeIndex = (array.length / 2) - 1;
        // it would make the loop faster to start with the last non-leaf node
        // where the heapify would check its children
        for (int i = lastNonLeafNodeIndex; i >= 0; i--) {
            heapify(array, i, array.length);
        }
    }

    private static void heapify(int[] array, final int currentIndex, final int arraySize) {
        int leftIndex = (2 * currentIndex) + 1;
        int rightIndex = (2 * currentIndex) + 2;
        int largestIndex = currentIndex;
        if (leftIndex < arraySize && array[leftIndex] > array[currentIndex]) {
            largestIndex = leftIndex;
        }
        if (rightIndex < arraySize && array[rightIndex] > array[largestIndex]) {
            largestIndex = rightIndex;
        }
        if (largestIndex != currentIndex) {
            swap(array, currentIndex, largestIndex);
            heapify(array, largestIndex, arraySize);
        }
    }

    private static int removeMax(int[] array, final int arraySize) {
        int max = array[0];
        array[0] = array[arraySize - 1];
        array[arraySize - 1] = Integer.MIN_VALUE; // this is not necessary but would be useful for visualization
        heapify(array, 0, arraySize - 1);
        return max;
    }

    private static void swap(int[] array, int i, int j) {
        int tmp = array[i];
        array[i] = array[j];
        array[j] = tmp;
    }
}
