package ds.array;

import static other.hashmap.Assertions.assertIntArrayEquals;

public class LeftRotation {
    public static void main(String[] args) {
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 0), new int[]{1, 2, 3, 4, 5});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 1), new int[]{2, 3, 4, 5, 1});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 2), new int[]{3, 4, 5, 1, 2});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 3), new int[]{4, 5, 1, 2, 3});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 4), new int[]{5, 1, 2, 3, 4});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 5), new int[]{1, 2, 3, 4, 5});
        assertIntArrayEquals(leftRotation(new int[]{1, 2, 3, 4, 5}, 6), new int[]{2, 3, 4, 5, 1});
    }

    private static int[] leftRotation(int[] sourceArray, int rotateNumber) {
        rotateNumber = rotateNumber % sourceArray.length;
        int maxMoves = sourceArray.length;
        int moves = 0;
        int index = 0;
        boolean tempIsSet = false;
        int tempValue = Integer.MAX_VALUE;
        while (moves < maxMoves) {
            int value;
            if (tempIsSet) {
                 value = tempValue;
            } else {
                value = sourceArray[index];
            }
            index = (sourceArray.length - rotateNumber + index) % sourceArray.length;
            tempValue = sourceArray[index];
            tempIsSet = true;
            sourceArray[index] = value;
            moves++;
        }
        return sourceArray;
    }
}
