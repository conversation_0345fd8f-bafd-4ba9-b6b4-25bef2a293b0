package ds.array;

import static other.hashmap.Assertions.assertIntArrayEquals;

/**
 * <a href="https://www.hackerrank.com/contests/coding-context-1/challenges/right-rotation-2">Rotation Array to right</a>
 */
public class RightRotation {
    public static void main(String[] args) {
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 0), new int[]{1, 2, 3, 4, 5});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 1), new int[]{5, 1, 2, 3, 4});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 2), new int[]{4, 5, 1, 2, 3});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 3), new int[]{3, 4, 5, 1, 2});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 4), new int[]{2, 3, 4, 5, 1});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 5), new int[]{1, 2, 3, 4, 5});
        assertIntArrayEquals(rightRotation(new int[]{1, 2, 3, 4, 5}, 6), new int[]{5, 1, 2, 3, 4});
    }

    private static int[] rightRotation(int[] sourceArray, int rotateNumber) {
        rotateNumber = rotateNumber % sourceArray.length;
        if (rotateNumber == 0) {
            return sourceArray;
        }
        reverse(sourceArray, 0, sourceArray.length - 1);
        reverse(sourceArray, 0, rotateNumber - 1);
        reverse(sourceArray, rotateNumber, sourceArray.length - 1);
        return sourceArray;
    }

    private static void reverse(int[] array, int startIndex, int endIndex) {
        for (int i = startIndex; i <= ((endIndex - startIndex) / 2) + startIndex; i++) {
            int temp = array[i];
            array[i] = array[endIndex - i + startIndex];
            array[endIndex - i + startIndex] = temp;
        }
    }
}
