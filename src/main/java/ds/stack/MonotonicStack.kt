package ds.stack

import java.util.*

fun main() {
    println(buildNextGreaterNumberMap(intArrayOf(11, 13, 21, 3)))
    println(buildNextGreaterNumberMap(intArrayOf(6, 5, 4, 3, 2, 1, 7)))
}

fun buildNextGreaterNumberMap(nums: IntArray): Map<Int, Int> {
    // time: O(n)
    // space: O(n) as each element would be pushed and popped once => 2n
    val result = mutableMapOf<Int, Int>()
    val stack = Stack<Int>()
    for (currentNum in nums) {
        while (stack.isNotEmpty() && stack.peek() < currentNum) {
            result[stack.pop()] = currentNum
        }
        stack.push(currentNum)
    }
    return result
}
