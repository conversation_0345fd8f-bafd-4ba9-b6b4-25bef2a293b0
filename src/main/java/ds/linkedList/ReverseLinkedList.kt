package ds.linkedList

import ds.SinglyLinkedListNode
import ds.printSinglyLinkedListNode

fun main() {
    val head = SinglyLinkedListNode(0)
    head.next = SinglyLinkedListNode(1)
    head.next?.next = SinglyLinkedListNode(2)
    head.next?.next?.next = SinglyLinkedListNode(3)
    head.next?.next?.next?.next = SinglyLinkedListNode(4)
    head.next?.next?.next?.next?.next = SinglyLinkedListNode(5)

    printSinglyLinkedListNode(head)

    printSinglyLinkedListNode(reverseLinkedList(head))
}

fun reverseLinkedList(head: SinglyLinkedListNode?): SinglyLinkedListNode? {
    var previous: SinglyLinkedListNode? = null
    var current = head
    while (current != null) {
        val next = current.next
        current.next = previous
        previous = current
        current = next
    }
    return previous
}