package goldman_sachs.find_indices_in_sentence

import org.amshove.kluent.`should be equal to`
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory

internal class FindIndicesInSentenceKtTest {
    @TestFactory
    fun `should able to find the indices in the sentence`() = listOf(
        listOf("An apple a day is good but two apples are better", "ap") to listOf(3, 31),
        listOf("An apple a day is good but two apples are better", "zz") to listOf(),
        listOf("An apple a day is good but two apples are better", "d") to listOf(11, 21),
        listOf("An apple a day is good but two apples are better", "Z") to listOf(),
    ).map { (data, expected) ->
        val sentence = data[0]
        val prefix = data[1]
        DynamicTest.dynamicTest("when sentence is $sentence and prefix is $prefix, then it should return $expected") {
            val actual = findIndicesInSentence(sentence, prefix)
            actual `should be equal to` expected
        }
    }
}