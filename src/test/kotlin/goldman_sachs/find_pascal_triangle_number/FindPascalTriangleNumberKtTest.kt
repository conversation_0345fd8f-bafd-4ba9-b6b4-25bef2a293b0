package goldman_sachs.find_pascal_triangle_number

import org.amshove.kluent.`should be equal to`
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.TestFactory


internal class FindPascalTriangleNumberKtTest {
    @TestFactory
    fun `should able to find the number in the pascal triangle at certain x y coordinate`() = listOf(
        /* n should not be 0 */
        Pair(0, 0) to 1,

        Pair(5, 0) to 1,
        <PERSON>ir(5, 1) to 5,
        Pair(5, 2) to 10,
        Pair(5, 3) to 10,
        Pair(5, 4) to 5,
        <PERSON>ir(5, 5) to 1,

        <PERSON>ir(6, 0) to 1,
        <PERSON>ir(6, 1) to 6,
        <PERSON>ir(6, 2) to 15,
        <PERSON>ir(6, 3) to 20,
        <PERSON>ir(6, 4) to 15,
        <PERSON>ir(6, 5) to 6,
        <PERSON>ir(6, 6) to 1,
    ).map { (coordinate, expected) ->
        dynamicTest("when coordinate is $coordinate, then it should return $expected") {
            val x = coordinate.second
            val y = coordinate.first
            val actual = findPascalTriangleNumber(x, y)
            actual `should be equal to` expected
        }
    }
}