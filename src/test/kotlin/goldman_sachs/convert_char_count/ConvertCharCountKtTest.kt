package goldman_sachs.convert_char_count

import org.amshove.kluent.`should be equal to`
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory

internal class ConvertCharCountKtTest {
    @TestFactory
    fun `should able to find the indices in the sentence`() = listOf(
        "aaabbc" to "a3b2c1",
        "aaaaa" to "a5",
        "abcde" to "a1b1c1d1e1",
        "aaabaaa" to "a3b1a3",
    ).map { (s, expected) ->
        DynamicTest.dynamicTest("when input is $s, then it should return $expected") {
            val actual = convertCharCount(s)
            actual `should be equal to` expected
        }
    }
}