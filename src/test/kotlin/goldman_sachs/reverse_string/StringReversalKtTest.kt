package goldman_sachs.reverse_string

import org.amshove.kluent.`should be equal to`
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.TestFactory

internal class StringReversalKtTest {
    @TestFactory
    fun `should able to find the indices in the sentence`() = listOf(
        "abc" to "cba",
        "abcd" to "dcba",
        "aaaa" to "aaaa",
        "1234123" to "3215321",
    ).map { (input, expected) ->
        dynamicTest("when input is $input, then it should return $expected") {
            val actual = reverseString(input)
            actual `should be equal to` expected
        }
    }
}